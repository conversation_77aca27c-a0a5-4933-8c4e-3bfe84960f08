package net.summerfarm.mall.service;

import net.summerfarm.mall.enums.payment.PaymentEnums;
import net.summerfarm.mall.mapper.MasterPaymentMapper;
import net.summerfarm.mall.mapper.PaymentMapper;
import net.summerfarm.mall.model.domain.MasterPayment;
import net.summerfarm.mall.model.domain.Payment;
import net.summerfarm.mall.payments.common.config.PaymentConfig;
import net.summerfarm.mall.payments.request.PaymentHandler;
import net.summerfarm.mall.service.impl.PaymentSyncServiceImpl;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * 支付状态同步服务测试
 */
@ExtendWith(MockitoExtension.class)
class PaymentSyncServiceTest {

    @Mock
    private PaymentMapper paymentMapper;

    @Mock
    private MasterPaymentMapper masterPaymentMapper;

    @Mock
    private PaymentHandler paymentHandler;

    @Mock
    private PaymentConfig paymentConfig;

    @InjectMocks
    private PaymentSyncServiceImpl paymentSyncService;

    @BeforeEach
    void setUp() {
        when(paymentConfig.getSyncDefaultLimit()).thenReturn(10);
        when(paymentConfig.getSyncMaxLimit()).thenReturn(100);
        when(paymentConfig.getSyncDefaultMinutes()).thenReturn(30);
    }

    @Test
    void testSyncPayingPayments() {
        // 准备测试数据
        LocalDateTime startTime = LocalDateTime.now().minusMinutes(30);
        LocalDateTime endTime = LocalDateTime.now();
        Integer limit = 10;

        Payment payment1 = createTestPayment("ORDER001", "WEIX");
        Payment payment2 = createTestPayment("ORDER002", "CMB");
        List<Payment> payments = Arrays.asList(payment1, payment2);

        MasterPayment masterPayment1 = createTestMasterPayment("MASTER001", "WEIX");
        List<MasterPayment> masterPayments = Collections.singletonList(masterPayment1);

        // Mock 方法调用 - 主支付单查询
        when(masterPaymentMapper.selectByStatusAndTimeRange(
            eq(PaymentEnums.PaymentStatus.PAYING.getStatus()),
            eq(startTime),
            eq(endTime),
            eq(limit)
        )).thenReturn(masterPayments);

        // Mock 方法调用 - 检查是否存在主支付单（用于决定是否查询普通支付单）
        when(masterPaymentMapper.selectByStatusAndTimeRange(
            eq(PaymentEnums.PaymentStatus.PAYING.getStatus()),
            eq(startTime),
            eq(endTime),
            eq(1)
        )).thenReturn(masterPayments); // 有主支付单，不查询普通支付单

        // 执行测试
        paymentSyncService.syncPayingPayments(startTime, endTime, limit);

        // 验证调用 - 只应该同步主支付单
        verify(masterPaymentMapper).selectByStatusAndTimeRange(
            eq(PaymentEnums.PaymentStatus.PAYING.getStatus()),
            eq(startTime),
            eq(endTime),
            eq(limit)
        );
        verify(paymentHandler).syncPaymentResultV2("MASTER001");

        // 不应该同步普通支付单（因为有主支付单）
        verify(paymentHandler, never()).syncPaymentResult(anyString());
    }

    @Test
    void testSyncPayingPaymentsOnlyRegularPayments() {
        // 测试只有普通支付单的情况
        LocalDateTime startTime = LocalDateTime.now().minusMinutes(30);
        LocalDateTime endTime = LocalDateTime.now();
        Integer limit = 10;

        Payment payment1 = createTestPayment("ORDER001", "WEIX");
        Payment payment2 = createTestPayment("ORDER002", "CMB");
        List<Payment> payments = Arrays.asList(payment1, payment2);

        // Mock 方法调用 - 没有主支付单
        when(masterPaymentMapper.selectByStatusAndTimeRange(
            eq(PaymentEnums.PaymentStatus.PAYING.getStatus()),
            eq(startTime),
            eq(endTime),
            eq(limit)
        )).thenReturn(Collections.emptyList());

        // Mock 方法调用 - 检查是否存在主支付单
        when(masterPaymentMapper.selectByStatusAndTimeRange(
            eq(PaymentEnums.PaymentStatus.PAYING.getStatus()),
            eq(startTime),
            eq(endTime),
            eq(1)
        )).thenReturn(Collections.emptyList()); // 没有主支付单，查询普通支付单

        // Mock 普通支付单查询
        when(paymentMapper.selectByStatusAndTimeRange(
            eq(PaymentEnums.PaymentStatus.PAYING.getStatus()),
            eq(startTime),
            eq(endTime),
            eq(limit)
        )).thenReturn(payments);

        // 执行测试
        paymentSyncService.syncPayingPayments(startTime, endTime, limit);

        // 验证调用 - 应该同步普通支付单
        verify(paymentMapper).selectByStatusAndTimeRange(
            eq(PaymentEnums.PaymentStatus.PAYING.getStatus()),
            eq(startTime),
            eq(endTime),
            eq(limit)
        );
        verify(paymentHandler).syncPaymentResult("ORDER001");
        verify(paymentHandler).syncPaymentResult("ORDER002");

        // 不应该同步主支付单（因为没有主支付单）
        verify(paymentHandler, never()).syncPaymentResultV2(anyString());
    }

    @Test
    void testSyncPaymentsByOrderNos() {
        // 准备测试数据
        List<String> orderNos = Arrays.asList("ORDER001", "ORDER002");

        Payment payment1 = createTestPayment("ORDER001", "WEIX");
        payment1.setStatus(PaymentEnums.PaymentStatus.PAYING.getStatus());

        Payment payment2 = createTestPayment("ORDER002", "CMB");
        payment2.setStatus(PaymentEnums.PaymentStatus.SUCCESS.getStatus()); // 已成功，不应该同步

        List<Payment> payments = Arrays.asList(payment1, payment2);

        // Mock 方法调用
        when(paymentMapper.selectByOrderNos(orderNos)).thenReturn(payments);

        // 执行测试
        paymentSyncService.syncPaymentsByOrderNos(orderNos);

        // 验证调用 - 只有支付中状态的订单会被同步
        verify(paymentMapper).selectByOrderNos(orderNos);
        verify(paymentHandler).syncPaymentResult("ORDER001");
        verify(paymentHandler, never()).syncPaymentResult("ORDER002");
    }

    @Test
    void testSyncMasterPaymentsByOrderNos() {
        // 准备测试数据
        List<String> masterOrderNos = Arrays.asList("MASTER001", "MASTER002");

        MasterPayment masterPayment1 = createTestMasterPayment("MASTER001", "WEIX");
        masterPayment1.setStatus(PaymentEnums.PaymentStatus.PAYING.getStatus());

        MasterPayment masterPayment2 = createTestMasterPayment("MASTER002", "CMB");
        masterPayment2.setStatus(PaymentEnums.PaymentStatus.SUCCESS.getStatus()); // 已成功，不应该同步

        List<MasterPayment> masterPayments = Arrays.asList(masterPayment1, masterPayment2);

        // Mock 方法调用
        when(masterPaymentMapper.selectByMasterOrderNos(masterOrderNos)).thenReturn(masterPayments);

        // 执行测试
        paymentSyncService.syncMasterPaymentsByOrderNos(masterOrderNos);

        // 验证调用 - 只有支付中状态的订单会被同步
        verify(masterPaymentMapper).selectByMasterOrderNos(masterOrderNos);
        verify(paymentHandler).syncPaymentResultV2("MASTER001");
        verify(paymentHandler, never()).syncPaymentResultV2("MASTER002");
    }

    @Test
    void testSyncPayingPaymentsWithLimitExceeded() {
        // 测试限制条数超过最大值的情况
        LocalDateTime startTime = LocalDateTime.now().minusMinutes(30);
        LocalDateTime endTime = LocalDateTime.now();
        Integer limit = 200; // 超过最大值100

        // 执行测试
        paymentSyncService.syncPayingPayments(startTime, endTime, limit);

        // 验证调用时使用了最大限制值
        verify(paymentMapper).selectByStatusAndTimeRange(
            eq(PaymentEnums.PaymentStatus.PAYING.getStatus()),
            eq(startTime),
            eq(endTime),
            eq(100) // 应该被调整为最大值
        );
    }

    private Payment createTestPayment(String orderNo, String payType) {
        Payment payment = new Payment();
        payment.setOrderNo(orderNo);
        payment.setPayType(payType);
        payment.setMoney(new BigDecimal("100.00"));
        payment.setStatus(PaymentEnums.PaymentStatus.PAYING.getStatus());
        payment.setCompanyAccountId(1);
        return payment;
    }

    private MasterPayment createTestMasterPayment(String masterOrderNo, String payType) {
        MasterPayment masterPayment = new MasterPayment();
        masterPayment.setMasterOrderNo(masterOrderNo);
        masterPayment.setPayType(payType);
        masterPayment.setMoney(new BigDecimal("200.00"));
        masterPayment.setStatus(PaymentEnums.PaymentStatus.PAYING.getStatus());
        masterPayment.setCompanyAccountId(1);
        return masterPayment;
    }
}
