package net.summerfarm.mall.mapper;

import net.summerfarm.mall.model.domain.Payment;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

@Repository
public interface PaymentMapper {
    int deleteByOrderNo(String orderNo);

    int insertSelective(Payment record);

    Payment selectOne(Payment selectKeys);

    int updateByPrimaryKeySelective(Payment record);

    int updateByPrimaryKey(Payment record);

    Map<String,Object> selectRefund(String orderno);

    int countSuccessByOrderNo(String orderno);

    int updateStatusById(@Param("id") Long paymentId, @Param("status") int status);

    void updateByOrderNo(Payment payment);

    /**
     * 根据订单号查询支付时间
     * @param orderNo
     * @return
     */
    LocalDateTime selectPayTimeByOrderNo(@Param("orderNo")String orderNo);
    LocalDateTime selectPayTimeByOrderNoForceMaster(@Param("orderNo")String orderNo);

    /**
     * 根据支付状态和时间段查询支付单
     * @param status 支付状态
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param limit 限制条数
     * @return 支付单列表
     */
    List<Payment> selectByStatusAndTimeRange(@Param("status") Integer status,
                                           @Param("startTime") LocalDateTime startTime,
                                           @Param("endTime") LocalDateTime endTime,
                                           @Param("limit") Integer limit);

    /**
     * 根据支付单号列表查询支付单
     * @param orderNos 支付单号列表
     * @return 支付单列表
     */
    List<Payment> selectByOrderNos(@Param("orderNos") List<String> orderNos);
}