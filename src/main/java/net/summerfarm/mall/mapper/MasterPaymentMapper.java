package net.summerfarm.mall.mapper;

import net.summerfarm.mall.model.domain.MasterPayment;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDateTime;
import java.util.List;

/**
* <AUTHOR>
* @description 针对表【master_payment(主单支付表)】的数据库操作Mapper
* @createDate 2023-10-11 14:09:11
* @Entity net.summerfarm.mall.model.domain.MasterPayment
*/
public interface MasterPaymentMapper {

    int deleteByPrimaryKey(Long id);

    int insert(MasterPayment record);

    int insertSelective(MasterPayment record);

    MasterPayment selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(MasterPayment record);

    int updateByPrimaryKey(MasterPayment record);

    /**
     * 查询支付单
     * @param masterPaymentNo 主支付单号
     * @return 支付单
     */
    MasterPayment selectByMasterPaymentNo(@Param("masterPaymentNo") String masterPaymentNo);

    /**
     * 根据支付单号更新支付单
     * @param record 支付单
     * @return 更新条数
     */
    int updateByMasterPaymentNoSelective(MasterPayment record);

    /**
     * 根据支付状态和时间段查询主支付单
     * @param status 支付状态
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param limit 限制条数
     * @return 主支付单列表
     */
    List<MasterPayment> selectByStatusAndTimeRange(@Param("status") Integer status,
                                                  @Param("startTime") LocalDateTime startTime,
                                                  @Param("endTime") LocalDateTime endTime,
                                                  @Param("limit") Integer limit);

    /**
     * 根据主支付单号列表查询主支付单
     * @param masterOrderNos 主支付单号列表
     * @return 主支付单列表
     */
    List<MasterPayment> selectByMasterOrderNos(@Param("masterOrderNos") List<String> masterOrderNos);
}
