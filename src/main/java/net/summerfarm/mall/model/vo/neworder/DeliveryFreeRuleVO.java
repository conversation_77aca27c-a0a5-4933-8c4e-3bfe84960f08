package net.summerfarm.mall.model.vo.neworder;

import java.io.Serializable;
import java.math.BigDecimal;
import lombok.Data;

/**
 * @author: <EMAIL>
 * @create: 2023/10/9
 */
@Data
public class DeliveryFreeRuleVO implements Serializable {

    /**
     * 商品类目  1：全部商品 2：乳制品商品  3：非乳制品商品
     */
    private Integer productType;

    /**
     * 门槛类型 1：金额  2：件数
     */
    private Integer sillType;

    /**
     * 件数  针对门槛类型为件数
     */
    private Integer number;

    /**
     * 金额 针对门槛类型为金额
     */
    private BigDecimal amount;

    /**
     * 还差x件数  针对门槛类型为件数
     */
    private Integer lackNumber;

    /**
     * 还差x金额 针对门槛类型为金额
     */
    private BigDecimal lackAmount;

}
