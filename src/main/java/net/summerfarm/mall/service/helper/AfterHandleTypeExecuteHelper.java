package net.summerfarm.mall.service.helper;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.common.AjaxResult;
import net.summerfarm.common.exceptions.DefaultServiceException;
import net.summerfarm.enums.AfterSaleOrderStatus;
import net.summerfarm.enums.OrderTypeEnum;
import net.summerfarm.enums.RefundStatusEnum;
import net.summerfarm.enums.SaleStockChangeTypeEnum;
import net.summerfarm.mall.common.util.DateUtils;
import net.summerfarm.mall.contexts.AfterSaleOrderAction;
import net.summerfarm.mall.contexts.Global;
import net.summerfarm.mall.enums.*;
import net.summerfarm.mall.mapper.*;
import net.summerfarm.mall.model.bo.coupon.CouponSenderBO;
import net.summerfarm.mall.model.domain.*;
import net.summerfarm.mall.model.vo.AfterSaleOrderVO;
import net.summerfarm.mall.model.vo.DeliveryPlanVO;
import net.summerfarm.mall.model.vo.OrderVO;
import net.summerfarm.mall.model.vo.ProductVO;
import net.summerfarm.mall.service.AfterSaleOrderService;
import net.summerfarm.mall.service.OrderService;
import net.summerfarm.mall.service.QuantityChangeRecordService;
import net.summerfarm.mall.service.facade.OfcQueryFacade;
import net.summerfarm.mall.service.facade.WmsAreaStoreFacade;
import net.summerfarm.mall.service.facade.dto.AreaStoreLockReq;
import net.summerfarm.mall.service.facade.dto.AreaStoreQueryRes;
import net.summerfarm.mall.service.facade.dto.OrderLockSkuDetailReqDTO;
import net.summerfarm.mall.service.strategy.coupon.CouponSenderContext;
import net.summerfarm.ofc.client.enums.OfcOrderSourceEnum;
import net.summerfarm.ofc.client.resp.DeliveryDateQueryResp;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Description 售后服务类型执行逻辑辅助类
 * @date 2022/8/3 1:18
 * @Version 1.0
 */
@Component
@Slf4j
public class AfterHandleTypeExecuteHelper {
    @Resource
    private OrdersMapper ordersMapper;
    @Resource
    private MerchantMapper merchantMapper;
    @Resource
    private InventoryMapper inventoryMapper;
    @Resource
    private DeliveryPlanMapper deliveryPlanMapper;
    @Resource
    private AreaMapper areaMapper;
    @Resource
    private AfterSaleOrderService afterSaleOrderService;
    @Resource
    private OrderService orderService;
    @Resource
    private ContactMapper contactMapper;
    @Resource
    private OrderItemMapper orderItemMapper;
    @Resource
    private QuantityChangeRecordService quantityChangeRecordService;
    @Resource
    private OfcQueryFacade getDeliveryDateFacade;
    @Resource
    private PrepayInventoryRecordMapper prepayInventoryRecordMapper;
    @Resource
    private PaymentMapper paymentMapper;
    @Resource
    private RefundMapper refundMapper;
    @Resource
    private MerchantCouponMapper merchantCouponMapper;
    @Resource
    private CouponMapper couponMapper;
    @Resource
    private DiscountCardUseRecordMapper discountCardUseRecordMapper;
    @Resource
    private DiscountCardToMerchantMapper discountCardToMerchantMapper;
    @Resource
    private AfterSaleOrderAction afterSaleOrderAction;
    @Resource
    private AfterSaleDeliveryPathMapper afterSaleDeliveryPathMapper;
    @Resource
    private AfterSaleDeliveryDetailMapper afterSaleDeliveryDetailMapper;
    @Resource
    private CouponSenderContext couponSenderContext;
    @Resource
    private WmsAreaStoreFacade areaStoreFacade;

    /**
     * 执行退款逻辑
     * @param afterSaleOrderVO
     * @return
     */
    public AjaxResult refund(AfterSaleOrderVO afterSaleOrderVO){
        log.info("插入退款信息了："+ JSON.toJSONString(afterSaleOrderVO));
        //老数据清理
        AjaxResult result = oldDataClear(afterSaleOrderVO);
        if (!AjaxResult.isSuccess(result)){
            return result;
        }
        //查询当前退款信息是否是预付商品
        List<PrepayInventoryRecord> records = prepayInventoryRecordMapper.selectByOrderNoAndSku(afterSaleOrderVO.getOrderNo(), afterSaleOrderVO.getSku());
        Inventory inventory = inventoryMapper.selectBySku(afterSaleOrderVO.getSku());
        //判断是否符合
        boolean handleNumDecide = CollectionUtils.isEmpty(records) && (afterSaleOrderVO.getHandleNum() == null || afterSaleOrderVO.getHandleNum().compareTo(BigDecimal.ZERO) <= 0) && !Objects.equals(inventory.getType(), InventoryTypeEnum.AGENT_PRODUCT.getType());
        if (handleNumDecide) {
            throw new DefaultServiceException(0,"售后金额不能小于零");
        }

        OrderVO orderVO = ordersMapper.selectByOrderyNo(afterSaleOrderVO.getOrderNo());
        String orderNo = afterSaleOrderVO.getOrderNo();
        int successPaymentCnt = paymentMapper.countSuccessByOrderNo(orderNo);
        if (successPaymentCnt == 0) {
            return AjaxResult.getErrorWithMsg("查询不到对应的支付单");
        }
        //生成退款单
        try {
            insertNormalRefund(afterSaleOrderVO,orderVO,inventory,records);
        } catch (Exception e) {
            log.error(e.getMessage());
            return AjaxResult.getErrorWithMsg("生成退款单异常");
        }

        //执行退款逻辑
        try {
            AjaxResult refund = afterSaleOrderService.refund(afterSaleOrderVO.getAfterSaleOrderNo());
            if(!AjaxResult.isSuccess(refund)){
                return AjaxResult.getErrorWithMsg(refund.getMsg());
            }
        } catch (Exception e) {
            log.info("退款失败", e);
            //退款状态改为失败
           return AjaxResult.getErrorWithMsg("退款失败");
        }
        return AjaxResult.getOK();
    }

    private AjaxResult oldDataClear(AfterSaleOrderVO afterSaleOrderVO) {
        //不是成功状态的售后单，退款单数据清除
        try {
            if (!afterSaleOrderVO.getStatus().equals(AfterSaleOrderStatus.SUCCESS.getStatus())){
                List<Refund> refunds = refundMapper.selectByAllAfterSaleOrderNo(afterSaleOrderVO.getAfterSaleOrderNo());
                if (!CollectionUtils.isEmpty(refunds)){
                    for (Refund refund : refunds) {
                        refund.setStatus((byte)RefundStatusEnum.FAIL.ordinal());
                        refundMapper.updateByPrimaryKeySelective(refund);
                    }
                }
            }
        } catch (Exception e) {
            log.error("旧退款单清理出现异常：{}",e);
            return AjaxResult.getErrorWithMsg("旧退款单清理异常");
        }
        return AjaxResult.getOK();
    }

    /**
     * 返优惠劵操作
     * @return
     */
    public AjaxResult coupon(AfterSaleOrderVO afterSaleOrderVO){
        Coupon coupon = couponMapper.selectByCode(Global.AFTER_SALE_COUPON_FRONT + afterSaleOrderVO.getHandleNum().setScale(2));
        if (coupon == null) {
            coupon = new Coupon();
            coupon.setAddTime(LocalDateTime.now());
            coupon.setCode(Global.AFTER_SALE_COUPON_FRONT + afterSaleOrderVO.getHandleNum().setScale(2));
            coupon.setMoney(afterSaleOrderVO.getHandleNum());
            coupon.setName("售后补偿券");
            coupon.setType((byte) 0);
            coupon.setThreshold(afterSaleOrderVO.getHandleNum().add(BigDecimal.valueOf(0.01)));
            coupon.setVaildTime(180);
            coupon.setGrouping(CouponGroupingEnum.AFTER_SALE.ordinal());
            coupon.setAgioType(1);
            coupon.setActivityScope(CouponActivityScopeEnum.OTHER.ordinal());
            coupon.setStatus(1);
            coupon.setAutoCreated(1);
            //默认不限领取次数和不限领取张数
            coupon.setQuantityClaimed(0);
            coupon.setGrantLimit(0);
            coupon.setCreator("系统默认");
            couponMapper.insertNewCoupon(coupon);
        }

        //发放优惠券
        CouponSenderBO couponSenderBO = new CouponSenderBO();
        couponSenderBO.setCoupon(coupon);
        couponSenderBO.setMId(afterSaleOrderVO.getmId());
        couponSenderBO.setReceiveType(CouponReceiveTypeEnum.OTHER.getCode());
        couponSenderContext.sendActivityCoupon(couponSenderBO);

        return AjaxResult.getOK();
    }

    /**
     * 生成补发单，司机在补发的时候会顺便换货
     * @param afterSaleOrderVO
     * @param orders
     * @return
     */
    @Transactional(propagation = Propagation.REQUIRED,rollbackFor=Exception.class)
    public AjaxResult backToStore(AfterSaleOrderVO afterSaleOrderVO,Orders orders){
        log.info("生成售后补发单时数据："+JSON.toJSONString(afterSaleOrderVO)+"订单数据:"+JSON.toJSONString(orders));
        Integer deliveryId = afterSaleOrderVO.getDeliveryId();
        if (deliveryId == null && !orders.getType().equals(OrderTypeEnum.TIMING.getId())){
            List<DeliveryPlanVO> deliveryPlanVOS = deliveryPlanMapper.selectByOrderNo(orders.getOrderNo());
            if (deliveryPlanVOS.size() >1 || CollectionUtils.isEmpty(deliveryPlanVOS)){
                return AjaxResult.getErrorWithMsg("配送状态异常");
            }
            deliveryId = deliveryPlanVOS.get(0).getId();
        }
        AjaxResult result = afterSaleOrderService.checkStock(orders.getOrderNo(), afterSaleOrderVO.getSku(),deliveryId, afterSaleOrderVO.getQuantity(), afterSaleOrderVO.getmId());
        if (!AjaxResult.isSuccess(result)){
            return result;
        }
        AreaStoreQueryRes queryStore = (AreaStoreQueryRes) result.getData();
        String orderNo = afterSaleOrderVO.getOrderNo();
        String sku = afterSaleOrderVO.getSku();
        Integer quantity = afterSaleOrderVO.getQuantity();
        afterSaleOrderVO.setQueryStore(queryStore);
        Merchant merchant = merchantMapper.selectOneByMid(afterSaleOrderVO.getmId());
        if(merchant == null){
            return AjaxResult.getErrorWithMsg("获取用户信息失败");
        }
        //换货补发，设置为成功
        afterSaleOrderVO.setStatus(AfterSaleOrderStatus.SUCCESS.getStatus());

        AfterSaleDeliveryPath insertPath = afterSaleOrderAction.createDeliveryPath(afterSaleOrderVO);
        afterSaleDeliveryPathMapper.insertAfterSaleDeliveryPath(insertPath);
        //生成售后配送单详情
        List<AfterSaleDeliveryDetail> details = new ArrayList<>();
        //补货详情
        AfterSaleDeliveryDetail deliveryDetail = afterSaleOrderAction.createDeliveryDetail(sku, quantity, insertPath.getId());
        details.add(deliveryDetail);
        List<ExchangeGoods> exchangeGoodList = afterSaleOrderVO.getExchangeGoodList();
        if(!CollectionUtils.isEmpty(exchangeGoodList)){
            details.addAll(Lists.transform(exchangeGoodList, x -> handleDetail(x, insertPath.getId())));
        }
        //配送地址id
        Long contactId = afterSaleOrderAction.getContactId(orderNo,afterSaleOrderVO.getDeliveryId());
        afterSaleDeliveryDetailMapper.insertAfterSaleDeliveryDetailList(details);

        //补发，换货冻结库存
        /*Map<String, QuantityChangeRecord> recordMap = new HashMap<>();
        try {
            orderService.updateStock(sku, merchant.getAreaNo(), -quantity,afterSaleOrderVO.getHandler(), SaleStockChangeTypeEnum.REFUNDS, orderNo, recordMap,false,contactId);
        } catch (Exception e) {
            log.error("换货补发时异常：{}",e);
            return AjaxResult.getErrorWithMsg("库存异常");
        }
        quantityChangeRecordService.insert(recordMap);*/

        //库存扣减 改用新模型
        AreaStoreLockReq storeLockReq = new AreaStoreLockReq();
        storeLockReq.setOrderNo(afterSaleOrderVO.getAfterSaleOrderNo());
        storeLockReq.setOperatorNo(afterSaleOrderVO.getAfterSaleOrderNo());
        storeLockReq.setIdempotentNo(afterSaleOrderVO.getAfterSaleOrderNo());
        storeLockReq.setOrderType(SaleStockChangeTypeEnum.REFUNDS.getTypeName());
        storeLockReq.setContactId(contactId);
        List<OrderLockSkuDetailReqDTO> orderLockSkuDetailReqDTOS = new ArrayList<>();
        OrderLockSkuDetailReqDTO orderLockSkuDetailReqDTO = new OrderLockSkuDetailReqDTO();
        orderLockSkuDetailReqDTO.setSkuCode(sku);
        orderLockSkuDetailReqDTO.setOccupyQuantity(quantity);
        orderLockSkuDetailReqDTOS.add(orderLockSkuDetailReqDTO);
        storeLockReq.setOrderLockSkuDetailReqDTOS(orderLockSkuDetailReqDTOS);
        storeLockReq.setMerchantId(afterSaleOrderVO.getmId());
        storeLockReq.setSource(DistOrderSourceEnum.getDistOrderAfterSaleSourceByOrderType(orders.getType()));
        storeLockReq.setOperatorName(afterSaleOrderVO.getHandler());
        areaStoreFacade.storeLock(storeLockReq);

        return AjaxResult.getOK();
    }

    /**
     * 生成补售后配送单信息
     * @param afterSaleOrderVO
     * @return
     */
    public AjaxResult generateAfterDelivery(AfterSaleOrderVO afterSaleOrderVO){
        Orders orders = ordersMapper.selectByOrderNo(afterSaleOrderVO.getOrderNo());
        if (orders == null) {
            return AjaxResult.getErrorWithMsg("订单不存在");
        }

        Merchant merchant = merchantMapper.selectOneByMid(afterSaleOrderVO.getmId());
        Integer areaNo = merchant.getAreaNo();
        Area area = areaMapper.selectByAreaNo(areaNo);
        ProductVO productVO = inventoryMapper.queryBySku(afterSaleOrderVO.getSku());
        //获取发起售后的数量
        Integer deliveryId = afterSaleOrderVO.getDeliveryId();
        //省心送id 存在说明是省心送未到货退款,获取配送计划的配送地址
        Long contactId = afterSaleOrderAction.getContactId(afterSaleOrderVO.getOrderNo(),deliveryId);
        //配送时间
        DeliveryDateQueryResp dateQueryResp = getDeliveryDateFacade.queryDeliveryDate(LocalDateTime.now(),afterSaleOrderVO.getmId(),contactId,null,null,
                DistOrderSourceEnum.getOfcSourceEnumAfterSaleByOrderType(orders.getType()),null);
        //生成售后配送单信息
        Contact contact = contactMapper.selectByPrimaryKey(contactId);
        Integer storeNo = contact.getStoreNo() ;
        AfterSaleDeliveryPath insertPath = new AfterSaleDeliveryPath();
        insertPath.setConcatId(contactId);
        insertPath.setAfterSaleNo(afterSaleOrderVO.getAfterSaleOrderNo());
        insertPath.setDeliveryTime(dateQueryResp.getDeliveryDate());
        insertPath.setMId(afterSaleOrderVO.getmId());
        insertPath.setOutStoreNo(storeNo);
        insertPath.setStatus(1);
        insertPath.setType(1);
        afterSaleDeliveryPathMapper.insertAfterSaleDeliveryPath(insertPath);
        //生成售后配送单详情
        AfterSaleDeliveryDetail insertDetail = new AfterSaleDeliveryDetail();
        insertDetail.setAsDeliveryPathId(insertPath.getId());
        insertDetail.setPdName(productVO.getPdName());
        insertDetail.setQuantity(afterSaleOrderVO.getQuantity());
        insertDetail.setWeight(productVO.getWeight());
        insertDetail.setSku(afterSaleOrderVO.getSku());
        insertDetail.setType(1);
        insertDetail.setStatus(AfterSaleDeliveryDetail.EFFECTIVE_STATUS);

        //退货退款、退货录入账单 假如指定回收商品需要根据指定sku进行回收
        if (!CollectionUtils.isEmpty(afterSaleOrderVO.getExchangeGoodList())) {
            ExchangeGoods exchangeGoods = afterSaleOrderVO.getExchangeGoodList().get(0);
            log.info("指定回收商品exchangeGoods:{}, afterSaleOrderVO:{}", JSON.toJSONString(exchangeGoods), JSON.toJSONString(afterSaleOrderVO));
            insertDetail.setSku(Optional.ofNullable(exchangeGoods.getSku()).orElse(afterSaleOrderVO.getSku()));
            insertDetail.setPdName(Optional.ofNullable(exchangeGoods.getPdName()).orElse(productVO.getPdName()));
            insertDetail.setWeight(Optional.ofNullable(exchangeGoods.getWeight()).orElse(productVO.getWeight()));
            insertDetail.setQuantity(Optional.ofNullable(exchangeGoods.getQuantity()).orElse(afterSaleOrderVO.getQuantity()));
        }
        afterSaleDeliveryDetailMapper.insertAfterSaleDeliveryDetail(insertDetail);
        return AjaxResult.getOK();
    }

    /**
     * 返奶油卡操作
     */
    public AjaxResult backCard(String orderNo){
        //更改使用次数,使用记录
        List<DiscountCardUseRecord> useRecords = discountCardUseRecordMapper.selectByOrderNo(orderNo);
        if (!CollectionUtils.isEmpty(useRecords)) {
            Map<Integer, List<DiscountCardUseRecord>> collect = useRecords.stream().collect(Collectors.groupingBy(DiscountCardUseRecord::getDiscountCardMerchantId));
            collect.forEach((cardId, records) -> {
                Integer discountCardMerchantId = records.get(0).getDiscountCardMerchantId();
                DiscountCardToMerchant discountCardToMerchant = discountCardToMerchantMapper.selectByPrimaryKey(discountCardMerchantId);
                int sum = records.stream().mapToInt(DiscountCardUseRecord::getUseTimes).sum();
                DiscountCardToMerchant update = new DiscountCardToMerchant();
                update.setId(discountCardMerchantId);
                update.setUsedTimes(Math.max(discountCardToMerchant.getUsedTimes() - sum, 0));
                update.setUpdateTime(LocalDateTime.now());
                discountCardToMerchantMapper.updateByPrimaryKeySelective(update);
                log.info("退款-执行返奶油卡操作，奶油卡信息-update:{}", JSON.toJSONString(update));
            });
        }
        return AjaxResult.getOK();
    }

    /**
     * 换货单详情
     */
    private AfterSaleDeliveryDetail handleDetail(ExchangeGoods exchangeGoods,Integer id){
        AfterSaleDeliveryDetail deliveryDetail = new AfterSaleDeliveryDetail();
        deliveryDetail.setSku(exchangeGoods.getSku());
        deliveryDetail.setWeight(exchangeGoods.getWeight());
        deliveryDetail.setPdName(exchangeGoods.getPdName());
        deliveryDetail.setQuantity(exchangeGoods.getQuantity());
        deliveryDetail.setStatus(AfterSaleDeliveryDetail.EFFECTIVE_STATUS);
        deliveryDetail.setAsDeliveryPathId(id);
        deliveryDetail.setType(AfterSaleDeliveryDetail.RECOVERY_TYPE);
        return deliveryDetail;
    }

    /**
     * 修改省心送订单项
     * @param orders
     * @param nowAfterQuantity
     * @return
     */
    public AjaxResult reviseTimingOrderStatus(Orders orders,Integer nowAfterQuantity){
        String orderNo = orders.getOrderNo();
        Integer afterSaleQuantity = afterSaleOrderService.getAfterSaleSuccessQuanlity(orderNo);
        afterSaleQuantity +=nowAfterQuantity;
        List<DeliveryPlanVO> plans = deliveryPlanMapper.selectByOrderNo(orderNo);
        int deliveredQuantity = plans.stream().filter(deliveryPlanVO -> !deliveryPlanVO.getDeliveryTime().isAfter(LocalDate.now())).mapToInt(dp -> dp.getQuantity()).sum();
        Integer quantity = orderItemMapper.selectTimingOrderQuantity(orderNo);

        log.info("orderNo:{}, deliveredQuanlity:{}, afterSaleQuanlity:{},quantity:{}", orderNo, deliveredQuantity, afterSaleQuantity, quantity);
        //配送计划的数量加上售后的数量等于订单下单的数量
        if( deliveredQuantity + afterSaleQuantity == quantity){
            if (deliveredQuantity == 0) {
                ordersMapper.updateStatus(orderNo, OrderStatusEnum.DRAWBACK.getId(), orders.getStatus());
            } else {
                int number = deliveryPlanMapper.countByOrderNo(orderNo);
                //如果待收货数量配送计划只有一个，改变订单状态，否则等其自动收货
                if (Objects.equals(number,1)){
                    //改变配送计划的订单状态
                    List<DeliveryPlanVO> deliveryPlanVOS = deliveryPlanMapper.selectByDeliveryPlan(orderNo);
                    for (DeliveryPlanVO deliveryPlanVO : deliveryPlanVOS) {
                        deliveryPlanMapper.deliveryedById(deliveryPlanVO.getId());
                    }
                    orderItemMapper.updateStatusByOrderNo(orderNo, OrderStatusEnum.RECEIVED.getId());
                    //改变订单状态，插入自动确认收货时间
                    orders.setConfirmTime(DateUtils.localDate2Date(LocalDate.now()));
                    //省心送订单变更为已经收货
                    ordersMapper.updateByOrderNoSelective(orders);
                    ordersMapper.updateStatus(orderNo, OrderStatusEnum.RECEIVED.getId(), orders.getStatus());
                    orderService.updateScore(orderNo, orders.getmId());
                    //orderService.pushOrderConfirm(orderNo);
                }
            }

        }
        return AjaxResult.getOK();
    }

    /**
     * 普通订单退款
     * @param afterSaleOrderVO
     * @param orderVO
     * @param inventory
     * @param records
     */
    public AjaxResult insertNormalRefund(AfterSaleOrderVO afterSaleOrderVO,OrderVO orderVO,Inventory inventory,List<PrepayInventoryRecord> records){

        //查询历史退款记录
        BigDecimal totalFen = orderVO.getTotalPrice().multiply(BigDecimal.valueOf(100));
        BigDecimal refundFen = afterSaleOrderVO.getHandleNum().multiply(BigDecimal.valueOf(100));
        log.info("refundFen={}",refundFen);
        // List<Refund> refunds = refundMapper.selectByOrderNo(afterSaleOrderVO.getOrderNo());
        String refundNo = Global.createAfterSaleOrderNo(afterSaleOrderVO.getOrderNo()); // afterSaleOrderVO.getOrderNo() + (refunds.size() + 1);
        Refund refund = new Refund(afterSaleOrderVO.getOrderNo(), afterSaleOrderVO.getAfterSaleOrderNo(), refundNo, totalFen, refundFen);
        if (inventory!=null){
            if(Objects.equals(inventory.getType(),1) || !CollectionUtils.isEmpty(records)){
                refund.setRefundChannel("鲜沐卡");
            }
        }
        refund.setCouponId(afterSaleOrderVO.getCouponId());
        refundMapper.insertSelective(refund);
        return AjaxResult.getOK();
    }

}
