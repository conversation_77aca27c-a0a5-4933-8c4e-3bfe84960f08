package net.summerfarm.mall.service.impl;

import lombok.extern.slf4j.Slf4j;
import net.summerfarm.mall.enums.payment.PaymentEnums;
import net.summerfarm.mall.mapper.MasterPaymentMapper;
import net.summerfarm.mall.mapper.PaymentMapper;
import net.summerfarm.mall.model.domain.MasterPayment;
import net.summerfarm.mall.model.domain.Payment;
import net.summerfarm.mall.payments.common.config.PaymentConfig;

import net.summerfarm.mall.payments.request.PaymentHandler;
import net.summerfarm.mall.service.PaymentSyncService;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 支付状态同步服务实现
 * <AUTHOR>
 * @date 2024-12-20
 */
@Slf4j
@Service
public class PaymentSyncServiceImpl implements PaymentSyncService {

    @Resource
    private PaymentMapper paymentMapper;

    @Resource
    private MasterPaymentMapper masterPaymentMapper;

    @Resource
    private PaymentHandler paymentHandler;

    @Resource
    private PaymentConfig paymentConfig;

    @Override
    public void syncPayingPayments(LocalDateTime startTime, LocalDateTime endTime, Integer limit) {
        // 参数校验和默认值处理
        if (limit == null) {
            limit = paymentConfig.getSyncDefaultLimit();
        }
        if (limit > paymentConfig.getSyncMaxLimit()) {
            limit = paymentConfig.getSyncMaxLimit();
            log.warn("限制条数超过最大值，调整为：{}", limit);
        }

        log.info("开始同步支付中状态的支付单，时间范围：{} - {}，限制条数：{}", startTime, endTime, limit);

        try {
            // 优先查询主支付单，避免重复处理
            List<MasterPayment> payingMasterPayments = masterPaymentMapper.selectByStatusAndTimeRange(
                PaymentEnums.PaymentStatus.PAYING.getStatus(), startTime, endTime, limit);

            if (!CollectionUtils.isEmpty(payingMasterPayments)) {
                log.info("找到{}条支付中状态的主支付单", payingMasterPayments.size());
                syncMasterPayments(payingMasterPayments);
            }

            // 查询没有对应主支付单的普通支付单（独立支付单）
            List<Payment> independentPayingPayments = findIndependentPayingPayments(startTime, endTime, limit);

            if (!CollectionUtils.isEmpty(independentPayingPayments)) {
                log.info("找到{}条独立的支付中状态支付单", independentPayingPayments.size());
                syncPayments(independentPayingPayments);
            }

            log.info("支付状态同步完成");
        } catch (Exception e) {
            log.error("同步支付状态时发生异常", e);
        }
    }

    @Override
    public void syncPaymentsByOrderNos(List<String> orderNos) {
        if (CollectionUtils.isEmpty(orderNos)) {
            log.warn("支付单号列表为空，跳过同步");
            return;
        }

        log.info("根据支付单号列表同步支付状态，单号数量：{}", orderNos.size());

        try {
            List<Payment> payments = paymentMapper.selectByOrderNos(orderNos);
            if (!CollectionUtils.isEmpty(payments)) {
                // 只同步支付中状态的支付单
                List<Payment> payingPayments = payments.stream()
                    .filter(payment -> PaymentEnums.PaymentStatus.PAYING.getStatus().equals(payment.getStatus()))
                    .collect(Collectors.toList());

                if (!CollectionUtils.isEmpty(payingPayments)) {
                    log.info("找到{}条支付中状态的支付单", payingPayments.size());
                    syncPayments(payingPayments);
                }
            }
        } catch (Exception e) {
            log.error("根据支付单号同步支付状态时发生异常", e);
        }
    }

    @Override
    public void syncMasterPaymentsByOrderNos(List<String> masterOrderNos) {
        if (CollectionUtils.isEmpty(masterOrderNos)) {
            log.warn("主支付单号列表为空，跳过同步");
            return;
        }

        log.info("根据主支付单号列表同步支付状态，单号数量：{}", masterOrderNos.size());

        try {
            List<MasterPayment> masterPayments = masterPaymentMapper.selectByMasterOrderNos(masterOrderNos);
            if (!CollectionUtils.isEmpty(masterPayments)) {
                // 只同步支付中状态的主支付单
                List<MasterPayment> payingMasterPayments = masterPayments.stream()
                    .filter(payment -> PaymentEnums.PaymentStatus.PAYING.getStatus().equals(payment.getStatus()))
                    .collect(Collectors.toList());

                if (!CollectionUtils.isEmpty(payingMasterPayments)) {
                    log.info("找到{}条支付中状态的主支付单", payingMasterPayments.size());
                    syncMasterPayments(payingMasterPayments);
                }
            }
        } catch (Exception e) {
            log.error("根据主支付单号同步支付状态时发生异常", e);
        }
    }

    /**
     * 同步普通支付单状态
     * @param payments 支付单列表
     */
    private void syncPayments(List<Payment> payments) {
        for (Payment payment : payments) {
            try {
                log.info("开始同步支付单：{}，支付类型：{}", payment.getOrderNo(), payment.getPayType());

                // 调用支付处理器同步状态
                paymentHandler.syncPaymentResult(payment.getOrderNo());

                log.info("支付单{}状态同步完成", payment.getOrderNo());
            } catch (Exception e) {
                log.error("同步支付单{}状态时发生异常：{}", payment.getOrderNo(), e.getMessage(), e);
            }
        }
    }

    /**
     * 同步主支付单状态
     * @param masterPayments 主支付单列表
     */
    private void syncMasterPayments(List<MasterPayment> masterPayments) {
        for (MasterPayment masterPayment : masterPayments) {
            try {
                log.info("开始同步主支付单：{}，支付类型：{}", masterPayment.getMasterOrderNo(), masterPayment.getPayType());

                // 调用支付处理器同步状态
                paymentHandler.syncPaymentResultV2(masterPayment.getMasterOrderNo());

                log.info("主支付单{}状态同步完成", masterPayment.getMasterOrderNo());
            } catch (Exception e) {
                log.error("同步主支付单{}状态时发生异常：{}", masterPayment.getMasterOrderNo(), e.getMessage(), e);
            }
        }
    }

    /**
     * 查找独立的支付中状态支付单（没有对应主支付单的支付单）
     * 这里使用一个简化的逻辑：如果没有主支付单，则查询普通支付单
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param limit 限制条数
     * @return 独立的支付单列表
     */
    private List<Payment> findIndependentPayingPayments(LocalDateTime startTime, LocalDateTime endTime, Integer limit) {
        // 先检查是否有主支付单，如果没有主支付单，则查询普通支付单
        List<MasterPayment> masterPayments = masterPaymentMapper.selectByStatusAndTimeRange(
            PaymentEnums.PaymentStatus.PAYING.getStatus(), startTime, endTime, 1);

        // 如果有主支付单，则不查询普通支付单，避免重复处理
        if (!CollectionUtils.isEmpty(masterPayments)) {
            log.info("存在主支付单，跳过普通支付单查询以避免重复处理");
            return Collections.emptyList();
        }

        // 没有主支付单时，查询普通支付单
        log.info("未找到主支付单，查询普通支付单");
        return paymentMapper.selectByStatusAndTimeRange(
            PaymentEnums.PaymentStatus.PAYING.getStatus(), startTime, endTime, limit);
    }

}
