package net.summerfarm.mall.service.impl;

import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageHelper;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.common.AjaxResult;
import net.summerfarm.common.delayqueue.DelayQueueItem;
import net.summerfarm.common.exceptions.DefaultServiceException;
import net.summerfarm.common.util.StringUtils;
import net.summerfarm.common.util.rocketmq.RocketMqMessageConstant;
import net.summerfarm.contexts.ResultConstant;
import net.summerfarm.enums.OrderTypeEnum;
import net.summerfarm.enums.order.OrderConfirmResultEnum;
import net.summerfarm.mall.common.delayqueue.OrderCancelItem;
import net.summerfarm.mall.common.delayqueue.VirtualOrderCancelItem;
import net.summerfarm.mall.common.mq.DelayData;
import net.summerfarm.mall.common.mq.MQData;
import net.summerfarm.mall.common.mq.MType;
import net.summerfarm.mall.common.util.DateUtils;
import net.summerfarm.mall.common.util.MailUtil;
import net.summerfarm.mall.common.util.PageInfoHelper;
import net.summerfarm.mall.common.util.RequestHolder;
import net.summerfarm.mall.contexts.Global;
import net.summerfarm.mall.contexts.MQTopicConstant;
import net.summerfarm.mall.contexts.OrderPayStatusConstant;
import net.summerfarm.mall.enums.*;
import net.summerfarm.mall.enums.order.FulfillmentWayEnum;
import net.summerfarm.mall.facade.ofc.OfcLogisticsQueryFacade;
import net.summerfarm.mall.facade.ofc.dto.OrderFulfillmentInfo;
import net.summerfarm.mall.mapper.*;
import net.summerfarm.mall.model.domain.*;
import net.summerfarm.mall.model.dto.order.DeliveryPointDTO;
import net.summerfarm.mall.model.dto.order.OrderDeliveryDTO;
import net.summerfarm.mall.model.dto.order.OrderItemInfoDTO;
import net.summerfarm.mall.model.input.CheckInput;
import net.summerfarm.mall.model.input.OrderBillExportReq;
import net.summerfarm.mall.model.vo.*;
import net.summerfarm.mall.model.vo.merchant.contact.ContactAddressRemark;
import net.summerfarm.mall.payments.request.PaymentHandler;
import net.summerfarm.mall.service.*;
import net.summerfarm.mall.service.helper.OrderServiceHelper;
import net.summerfarm.mall.task.AsyncTaskService;
import net.summerfarm.mall.wechat.templatemessage.TemplateMsgSender;
import net.summerfarm.mall.wechat.templatemessage.TimeoutCloseMsg;
import net.xianmu.common.cache.InMemoryCache;
import net.xianmu.common.exception.BizException;
import net.xianmu.common.result.CommonResult;
import net.xianmu.rocketmq.support.producer.MqProducer;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.dao.DataIntegrityViolationException;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.*;
import java.util.stream.Collectors;


/**
 * @Package: net.summerfarm.trade.service.impl
 * @Description: 订单相关
 * @author: <EMAIL>
 * @Date: 2016/9/30
 */
@Slf4j
@Service
public class OrderServiceImpl implements OrderService {

    @Resource
    private OrdersMapper ordersMapper;
    @Resource
    private MerchantCouponMapper merchantCouponMapper;
    @Resource
    private MerchantMapper merchantMapper;
    @Resource
    private InventoryMapper inventoryMapper;
    @Resource
    private OrderItemMapper orderItemMapper;
    @Resource
    private TrolleyMapper trolleyMapper;
    @Resource
    private DeliveryPlanMapper deliveryPlanMapper;
    @Resource
    private ContactMapper contactMapper;
    @Resource
    private AreaMapper areaMapper;
    @Resource
    private TemplateMsgSender templateMsgSender;
    @Resource
    private AreaSkuMapper areaSkuMapper;
    @Resource
    private PaymentMapper paymentMapper;
    @Resource
    private MarketRuleHistoryMapper marketRuleHistoryMapper;
    @Resource
    private AfterSaleOrderMapper afterSaleOrderMapper;
    @Resource
    @Lazy
    private AsyncTaskService asyncTaskService;
    @Resource
    private MerchantCardRecordMapper merchantCardRecordMapper;
    @Resource
    private CardRuleMapper cardRuleMapper;
    @Resource
    private QuantityChangeRecordService quantityChangeRecordService;
    @Resource
    private OrderPreferentialMapper orderPreferentialMapper;
    @Resource
    private DiscountCardMapper discountCardMapper;
    @Resource
    private PrepayInventoryService prepayInventoryService;
    @Resource
    private PrepayInventoryRecordMapper prepayInventoryRecordMapper;
    @Resource
    private AdminMapper adminMapper;
    @Resource
    private MerchantOuterMapper merchantOuterInfoMapper;
    @Resource
    private OrderOuterInfoMapper orderOuterInfoMapper;
    @Resource
    private MerchantSubAccountMapper merchantSubAccountMapper;
    @Resource
    private AfterSaleOrderService afterSaleOrderService;
    @Resource
    private FenceService fenceService;
    @Resource
    private ConfigService configService;
    @Resource
    private PaymentHandler paymentHandler;
    @Resource
    private MajorPriceMapper majorPriceMapper;
    @Resource
    private MerchantService merchantService;
    @Resource
    private OrderServiceHelper orderServiceHelper;
    @Resource
    private DeliveryService deliveryService;
    @Resource
    private ActivityService activityService;
    @Resource
    private RedisTemplate redisTemplate;
    @Resource
    private OrderRelationService orderRelationService;
    @Resource
    private OrderExportRecordMapper orderExportRecordMapper;
    @Resource
    private MailUtil mailUtil;
    @Resource
    private DeliverPlanRemarkSnapshotService deliverPlanRemarkSnapshotService;
    @Resource
    private OrdersCouponMapper ordersCouponMapper;

    @Resource
    private TimingOrderRefundTimeMapper timingOrderRefundTimeMapper;

    @Resource
    private TimingRuleMapper timingRuleMapper;

    @Autowired
    MqProducer mqProducer;

    @Autowired
    private OfcLogisticsQueryFacade ofcLogisticsQueryFacade;

    @Override
    @Transactional(propagation = Propagation.REQUIRED)
    public String userConfirm(String orderNo) {
        log.info("订单:{}发送收货请求", orderNo);
        return confirm(orderNo, RequestHolder.getMId());
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED)
    public void orderConfirm(DeliveryPlanVO deliveryPlanVO) {
        String orderNo = deliveryPlanVO.getOrderNo();
        Long mId = deliveryPlanVO.getmId();
        Integer orderType = deliveryPlanVO.getType();

        if (orderType == null) {
            throw new DefaultServiceException(1, "自动确认收货异常！订单号{}没有类型", orderNo);
        }

        //如果是省心送订单
        if (orderType.intValue() == OrderTypeEnum.TIMING.getId()) {
            timingOrderConfirm(orderNo, deliveryPlanVO.getQuantity(), mId, deliveryPlanVO.getId());
            orderServiceHelper.firstOrderSendCoupon(mId, deliveryPlanVO.getType(), orderNo);
        }
        //如果是普通订单(代下单)
        if (orderType.intValue() == OrderTypeEnum.NORMAL.getId() ||
                orderType.intValue() == OrderTypeEnum.HELP.getId() ||
                orderType.intValue() == OrderTypeEnum.POP.getId()) {
                String confirmStatus = confirm(orderNo, mId);
                log.info("{}普通订单确认收货状态:{},",orderNo, confirmStatus);
        }
    }

    @Override
    public AjaxResult getOrders(int pageIndex, int pageSize, Short orderStatus) {
        //查询出对应状态的订单
        PageHelper.startPage(pageIndex, pageSize).setOrderBy("o.order_time DESC ");
        List<OrderVO> orderVOs = ordersMapper.selectOrderList(RequestHolder.getMId(), orderStatus);
        //查询对应订单的商品条目
        for (OrderVO orderVO : orderVOs) {
            List<OrderItemVO> orderItems = orderItemMapper.selectOrderItemVO(orderVO.getOrderNo());
            //设置订单配送状态。
            List<DeliveryPlanVO> deliveryPlanVOS = deliveryPlanMapper.selectByOrderNoIntercept(orderVO.getOrderNo());
            if(!CollectionUtils.isEmpty(deliveryPlanVOS)){
                orderServiceHelper.handleDeliveryPlans(orderVO, deliveryPlanVOS);
            }else {
                orderVO.setDeliveryStatus(DeliveryStatusEnum.NOT_YET.getStatus());
            }

            orderVO.setOrderItems(orderItems);
            orderVO.setmName(RequestHolder.getName());
            if (Objects.equals(orderVO.getType(), OrderTypeEnum.TIMING.getId())){
                TimingOrderRefundTime timingOrderRefundTime =  timingOrderRefundTimeMapper.selectByOrderNo(orderVO.getOrderNo());
                if (!ObjectUtils.isEmpty(timingOrderRefundTime)){
                    orderVO.setRefundTime(timingOrderRefundTime.getRefundTime());
                }
            }
        }
        return AjaxResult.getOK(PageInfoHelper.createPageInfo(orderVOs));
    }

    @Override
    public AjaxResult orderDetails(String orderNo) {
        Orders record = ordersMapper.selectOne(orderNo);
        if (Objects.isNull(record)) {
            return AjaxResult.getErrorWithMsg("请稍后查询");
        }
        if (Objects.equals(record.getType(), OrderTypeEnum.NORMAL.getId()) || Objects.equals(record.getType(), OrderTypeEnum.PANIC_BUY.getId()) || Objects.equals(record.getType(), OrderTypeEnum.POP.getId())) {
            return AjaxResult.getOK(normalOrderDetail(orderNo));
        }
        if (Objects.equals(record.getType(), OrderTypeEnum.TIMING.getId())) {
            return AjaxResult.getOK(orderServiceHelper.buildTimingOrderDetail(orderNo));
        }
        return AjaxResult.getErrorWithMsg("订单类型有误");
    }

    @Override
    public OrderVO normalOrderDetail(String orderNo) {
        // 查询订单基本数据
        List<OrderVO> orderVOs = ordersMapper.selectOrder(orderNo);
        if(CollectionUtils.isEmpty(orderVOs)){
            return new OrderVO();
        }
        OrderVO orderVO = orderVOs.get(0);

        // 查询订单关联的活动数据
        List<OrderPreferential> orderPreferentialList = orderPreferentialMapper.selectPreferential(orderNo);
        orderVO.setPreferentials(orderPreferentialList);

        //计算活动优惠金额
        BigDecimal skuPriceDiscount = BigDecimal.ZERO;
        BigDecimal orderDiscount = BigDecimal.ZERO;
        for (OrderPreferential el : orderPreferentialList) {
            if (OrderPreferentialTypeEnum.isSkuPriceDiscount(el.getType())) {
                skuPriceDiscount = skuPriceDiscount.add(el.getAmount());
            }
            if (OrderPreferentialTypeEnum.isOrderPriceDiscount(el.getType())) {
                orderDiscount = orderDiscount.add(el.getAmount());
            }
        }
        orderVO.setDiscount(skuPriceDiscount);
        orderVO.setEventDiscount(orderDiscount);

        /*
         * 处理优惠券金额，此处需要根据订单是否存在主单处理优惠钱金额
         * 如果不存在主单，订单使用优惠券金额即为优惠券面值
         * 如果存在主单，需要根据优惠明细分摊优惠金额
         */
        Map<String, OrderRelation> orderNoMap = orderRelationService.queryMasterOrderNoByOrderNo(Collections.singletonList(orderNo));
        if (CollectionUtils.isEmpty(orderNoMap)){
            orderServiceHelper.handleOrdersCoupon(orderVO);
        } else {
            OrderRelation orderRelation = orderNoMap.get(orderNo);
            orderVO.setMasterOrderNo(orderRelation.getMasterOrderNo());
            orderVO.setPrecisionDeliveryFee(orderRelation.getPrecisionDeliveryFee());
            orderServiceHelper.handleOrdersCouponV2(orderVO, orderRelation.getMasterOrderNo(), orderPreferentialList);
        }

        // 查询订单下的商品集合
        List<OrderItemVO> orderItems = orderItemMapper.selectOrderItemVO(orderVO.getOrderNo());
        if(Objects.equals(orderVO.getmSize(),Global.BIG_MERCHANT)){
            orderServiceHelper.handlePrepayOrderItem(orderNo, orderItems);
        }
        if (StringUtils.isNotBlank(orderVO.getAddressRemark())){
            orderVO.setContactAddressRemark(JSONUtil.toBean(orderVO.getAddressRemark(),ContactAddressRemark.class));
        }

        //获取订单快照信息
        getOrderItemInfo(orderItems);

        //根据订单号获取配送计划
        List<DeliveryPlanVO> deliveryPlanVOS = deliveryPlanMapper.selectByMasterOrderNoVO(orderNo);
        if (!CollectionUtils.isEmpty(deliveryPlanVOS)){
            ContactAddressRemark contactAddressRemark = deliveryPlanVOS.get(0).getContactAddressRemark();
            if (contactAddressRemark!=null){
                orderVO.setContactAddressRemark(contactAddressRemark);
            }
            deliverPlanRemarkSnapshotService.mergeDeliveryPlanVOSSnapshot(deliveryPlanVOS);
        }

        //售后信息补充
        orderItems.forEach(o -> {
            //不是售后状态
            if (o.getStatus() != 8) {
                AfterSaleOrder selectAfter = new AfterSaleOrder();
                selectAfter.setOrderNo(orderVO.getOrderNo());
                selectAfter.setSku(o.getSku());
                selectAfter.setSuitId(o.getSuitId());
                selectAfter.setDeliveryed(0);
                selectAfter.setStatus(2);
                selectAfter.setProductType(o.getProductType());
                List<AfterSaleOrderVO> afterSaleOrderVOS = afterSaleOrderService.selectAfterSaleOrderVO(selectAfter);
                if (!CollectionUtils.isEmpty(afterSaleOrderVOS)) {
                    o.setStatus(12); // 12代表啥意思?
                }
            }
        });

        orderVO.setOrderItems(orderItems);
        orderVO.setMarketRuleHistoryList(marketRuleHistoryMapper.select(orderVO.getOrderNo(), 1));
        //商品原价总价
        BigDecimal skuTotalOriginPrice = BigDecimal.ZERO;
        for (OrderItemVO el : orderItems) {
            skuTotalOriginPrice = skuTotalOriginPrice.add(el.getOriginalPrice().multiply(BigDecimal.valueOf(el.getAmount())));
        }
        orderVO.setSkuTotalOriginPrice(skuTotalOriginPrice);

        orderVO.setDeliveryEvaluationStatus(
                orderServiceHelper.getDeliveryEvlStatus(
                        orderVO.getAreaNo(),
                        orderVO.getDeliveryStatus(),
                        orderVO.getDeliveryEvaluationStatus(),
                        orderVO.getDeliveryTime()
                        ));

        // 补充订单的配送方式、快递信息、发货日期
        OrderFulfillmentInfo orderFulfillmentInfo = ofcLogisticsQueryFacade.queryNormalOrderLogisticsInfo(orderVO.getOrderNo());
        if (null != orderFulfillmentInfo){
            orderVO.setOrderFulfillmentType(orderFulfillmentInfo.getOrderFulfillmentType());
            orderVO.setLogisticsInfoList(orderFulfillmentInfo.getLogisticsInfoList());
            if (FulfillmentWayEnum.EXPRESS.getValue().equals(orderFulfillmentInfo.getOrderFulfillmentType())){
                orderVO.setShipmentDate(orderVO.getDeliveryTime().minusDays(1L));
            }
        }
        return orderVO;
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED)
    public void timeOutClose(OrderCancelItem orderCancelItem) {
        Orders orders = ordersMapper.selectByOrderNo(orderCancelItem.getOrderNo());
        //判断订单状态
        if(Objects.isNull(orders) ){
            log.info("订单：{}不存在", orderCancelItem.getOrderNo());
            return;
        }
        if (orders.getStatus() != OrderStatusEnum.NO_PAYMENT.getId()) {
            log.info("订单：{},无需关单;订单状态：{}", orderCancelItem.getOrderNo(), orders.getStatus());
            return;
        }
        //调用关单接口
        String orderNo = orderCancelItem.getOrderNo();
        Long mId = orderCancelItem.getmId();
        String orderTime = DateUtils.date2String(new Date(orderCancelItem.getRunTime() - 30 * 60 * 1000), DateUtils.LONG_DATE_FORMAT);
        Merchant merchant = merchantMapper.selectOneByMid(mId);

        log.info("订单" + orderNo + "开始关单...");
        boolean result = handleOrderCancel(orderNo, merchant);
        if (result) {
            //发送关闭订单微信消息
            String openId = merchant.getOpenid();
            String msg = TimeoutCloseMsg.templateMessage(orderNo, orderTime, openId);
            log.info("订单" + orderNo + "微信超时关闭订单发送消息结果：" + msg);
            try {
                templateMsgSender.sendTemplateMsg(msg);
            } catch (Exception e){
                log.error("订单" + orderNo + "微信超时关闭订单发送消息失败", e);
            }
        }
    }

    @Override
    public AjaxResult selectOrderDetail(String orderNo) {
        OrderVO orderVO = ordersMapper.selectOrderVO(orderNo);
        if (orderVO == null) {
            log.warn("订单:{}异常", orderNo);
            return AjaxResult.getErrorWithMsg("订单不存在");
        }
        List<OrderItemVO> orderItems = orderItemMapper.selectOrderItemVO(orderNo);
        BigDecimal originPrice = orderVO.getOriginPrice();

        //大客户查看订单详情，减去预付商品应付金额
        List<PrepayInventoryRecord> prepayInventoryRecords = prepayInventoryRecordMapper.selectRecordListByOrderNo(orderNo);
        if(!CollectionUtils.isEmpty(prepayInventoryRecords)){
            Map<String, List<PrepayInventoryRecord>> collect =
                    prepayInventoryRecords.stream().collect(Collectors.groupingBy(PrepayInventoryRecord::getSku));
            for (OrderItemVO orderItem : orderItems) {
                //实付是0
                if(!CollectionUtils.isEmpty(collect.get(orderItem.getSku())) && orderItem.getPrice().compareTo(BigDecimal.ZERO) <= 0){
                    originPrice = originPrice.subtract(orderItem.getOriginalPrice().multiply(BigDecimal.valueOf(orderItem.getAmount())));
                    orderItem.setPrePayAmount(orderItem.getAmount());
                }
            }
        }
        //大客户遍历减去代仓商品的金额
        for (OrderItemVO orderItem : orderItems) {
            if(Objects.equals(orderItem.getSkuType(),1)){
                originPrice = originPrice.subtract(orderItem.getOriginalPrice().multiply(BigDecimal.valueOf(orderItem.getAmount())));
            }
        }
        orderVO.setOriginPrice(originPrice);
        List<OrderPreferential> orderPreferentials = orderPreferentialMapper.selectPreferential(orderNo);
        orderVO.setPreferentials(orderPreferentials);
        orderVO.setmName(RequestHolder.getName());
        orderVO.setOrderItems(orderItems);
        orderVO.setMarketRuleHistoryList(marketRuleHistoryMapper.select(orderNo, 1));
        if (orderVO.getCardRuleId() != null) {
            orderVO.setCardRuleVO(cardRuleMapper.select(orderVO.getCardRuleId()));
        }
        if (Objects.equals(orderVO.getType(), OrderTypeEnum.TIMING.getId())){
            TimingOrderRefundTime timingOrderRefundTime =  timingOrderRefundTimeMapper.selectByOrderNo(orderVO.getOrderNo());
            if (!ObjectUtils.isEmpty(timingOrderRefundTime)){
                orderVO.setRefundTime(timingOrderRefundTime.getRefundTime());
            }
        }
        return AjaxResult.getOK(orderVO);
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED)
    public AjaxResult cancelOrder(String orderNo) {
        Long mId = RequestHolder.getMId();
        log.info("用户：{},发起关闭订单:{}", mId, orderNo);
        Merchant merchant = merchantMapper.selectOneByMid(mId);
        orderServiceHelper.checkBeforeCancelOrder(orderNo, merchant);
        //直接关单,不判断是否满五分钟
        boolean result = handleOrderCancel(orderNo, merchant);
        if (result) {
            return AjaxResult.getOK();
        }
        return AjaxResult.getError("Fail", "订单关闭失败");
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED)
    public synchronized void updateStock(String sku, Integer areaNo, Integer quantity, String name, StockChangeType changeType,
                                         String recordNo, Map<String,QuantityChangeRecord> recordMap,Boolean handleReserve,Long contactId) {
        try {
            if(!handleReserve && quantity < 0){
                //单店客户下单校验虚拟库存可用数量
                Integer storeQuantity = orderServiceHelper.queryStoreQuantity(sku,contactId);
                if(storeQuantity < -quantity){
                    throw new DefaultServiceException(0,"库存不足,下单失败");
                }
            }
            //获取地址对应的配送仓
            Contact contact = contactMapper.selectByPrimaryKey(contactId);
            if(Objects.isNull(contact) || Objects.isNull(contact.getStoreNo())){
                throw new DefaultServiceException(0,"当前地址不在配送范围");
            }
            Integer storeNo = contact.getStoreNo();

            log.info("扣除库存：" + sku + "，城市：" + areaNo);
            AreaStore areaStore = orderServiceHelper.updateOnlineStock(sku, storeNo, quantity, name, changeType,recordNo,recordMap);
            orderServiceHelper.updateLockQuantity(storeNo, quantity, areaStore, changeType, name, recordNo, recordMap,handleReserve);
        } catch (DataIntegrityViolationException e) {
            throw new DefaultServiceException(0, sku + "在" + areaNo + "冻结库存不足");
        }

    }

    @Override
    public void updateScore(String orderNo, Long mId) {
        log.info("现在基于BI数据更新，这里不做处理。orderNo：{}，mId:{}", orderNo, mId);
    }

    private void timingOrderConfirm(String orderNo, int deliveryQuantity, Long mId, int deliveryId) {
        //省心送总数量
        Integer quantity = orderItemMapper.selectTimingOrderQuantity(orderNo);
        int deliveryed = deliveryPlanMapper.selectDeliveredByOrderNo(orderNo);
        Integer afterSaleQuantity = afterSaleOrderService.getAfterSaleSuccessQuanlity(orderNo);
        log.info("orderNo:{}, deliveryed:{},deliveryQuanlity:{}, afterSaleQuanity:{}", orderNo, deliveryed, deliveryQuantity, afterSaleQuantity);
        if (deliveryQuantity + deliveryed + afterSaleQuantity == quantity) {
            Orders orders = new Orders();
            orders.setOrderNo(orderNo);
            orders.setConfirmTime(DateUtils.localDate2Date(LocalDate.now()));
            orders.setStatus((short) 6);
            //省心送订单变更为已经收货
            ordersMapper.updateByOrderNoSelective(orders);
            orderItemMapper.updateStatusByOrderNoDelivery(orderNo, 6);
            updateScore(orderNo,mId);
            //pushOrderConfirm(orderNo);
            //省心送订单退款删除
            timingOrderRefundTimeMapper.deleteByOrderNo(orderNo);
            log.info(orderNo + "省心送自动确认收货！");
        }
        deliveryPlanMapper.deliveryedById(deliveryId);
    }

    private String confirm(String orderNo, Long mId) {
        Orders orders = ordersMapper.selectByOrderNo(orderNo);
        if (orderServiceHelper.checkBeforeConfirm(orderNo, orders)) {
            return OrderConfirmResultEnum.FAIL.getResult();
        }
        //1.更新订单状态
        Orders record = new Orders();
        record.setOrderNo(orderNo);
        record.setStatus(Short.valueOf("6"));
        record.setConfirmTime(new Date());
        ordersMapper.updateByOrderNoSelective(record);
        List<DeliveryPlanVO> dpVO = deliveryPlanMapper.selectByOrderNo(orderNo);
        deliveryPlanMapper.deliveryedById(dpVO.get(0).getId());
        //更新订单项状态
        orderItemMapper.updateStatusByOrderNoDelivery(orderNo, OrderStatusEnum.RECEIVED.getId());
        if (Objects.equals(orders.getType(), OrderTypeEnum.NORMAL.getId())) {
            orderServiceHelper.afterUserConfirm(orders, mId);
        }
        updateScore(orderNo,mId);
        //pushOrderConfirm(orderNo);
        orderServiceHelper.firstOrderSendCoupon(orders.getmId(), orders.getType(), orderNo);
        return OrderConfirmResultEnum.SUCCESS.getResult();
    }

    /**
     * 取消订单
     *
     * @param orderNo
     * @param merchant
     */
    private boolean handleOrderCancel(String orderNo, Merchant merchant) {
        //调用关单接口
        try {
            paymentHandler.closeOrder(orderNo);
        } catch (Exception e) {
            // 允许关单失败，记录异常即可
            log.error("关闭支付单{}失败", orderNo, e);
        }
        // 这里支付回调mq可能有延迟，反查支付成功后直接存入redis，这里判断使用支付成功不再取消订单
        if (redisTemplate.hasKey(OrderPayStatusConstant.ORDER_PAY_SUCCESS_STATUS + orderNo)){
            log.error("关闭订单{}失败,订单支付成功", orderNo);
            redisTemplate.delete(OrderPayStatusConstant.ORDER_PAY_SUCCESS_STATUS + orderNo);
            return false;
        }
        //1.在订单表中更新已撤销信息
        int rs = ordersMapper.updateStatus(orderNo, OrderStatusEnum.CANCEL.getId(), OrderStatusEnum.NO_PAYMENT.getId());
        if (rs != 1) {
            log.error("关闭订单{}失败,订单状态异常", orderNo);
            return false;
        }
        //2.释放优惠券（兼用下单后合并账号的场景）
        merchantCouponMapper.updateOrderNo(orderNo, merchant.getmId());
        //3.更改优惠卡使用次数
        merchantCardRecordMapper.deleteByOrderNo(orderNo);
        //4.更改黄金卡使用次数,使用记录
        orderServiceHelper.handleDiscountCard(orderNo);
        //5.释放在线库存信息
        List<OrderItem> orderItems = orderItemMapper.selectOrderItemByArea(orderNo, merchant.getAreaNo());
        orderItems = orderItems.stream().filter(orderItem -> !Global.TIME_FRAME_FEE_SKU.equals(orderItem.getSku())).collect(Collectors.toList());

        Orders cancelOrder = ordersMapper.selectByOrderNo(orderNo);
        boolean bigMerchant = merchantService.checkCoreCustomers(merchant.getmId());
        if (OrderTypeEnum.NORMAL.getId().equals(cancelOrder.getType()) || OrderTypeEnum.POP.getId().equals(cancelOrder.getType())) {
            List<DeliveryPlanVO> deliveryPlanVOS = deliveryPlanMapper.selectByOrderNoNoStatus(orderNo);
            DeliveryPlanVO deliveryPlanVO = deliveryPlanVOS.get(0);
            Long contactId = deliveryPlanVO.getContactId();
            Map<String,QuantityChangeRecord> recordMap = new HashMap<>();
            //预售没有冻结库存 没有虚拟
                List<String> skus = new ArrayList<>();

                //处理sku顺序、避免死锁
                orderItems.sort(Comparator.comparing(OrderItem::getSku));
                for (OrderItem orderItem : orderItems) {
                    skus.add(orderItem.getSku());
                    updateStock(orderItem.getSku(), cancelOrder.getAreaNo(), orderItem.getAmount(), merchant.getMname(), SaleStockChangeTypeEnum.CANCEL_ORDER,orderNo, recordMap,bigMerchant,contactId);
                    activityService.orderCancelActivityQuantity(orderNo,orderItem.getSku(),merchant.getmId());
                }
                //随单送扣库存
                List<DeliveryPlan> deliveryPlans = deliveryPlanMapper.selectByMasterOrderNo(orderNo);
                if (!CollectionUtils.isEmpty(deliveryPlans)) {
                    for (DeliveryPlan deliveryPlan : deliveryPlans) {
                        List<OrderItem> deliveryPlanOrderItems = orderItemMapper.selectOrderItemByArea(deliveryPlan.getOrderNo(), merchant.getAreaNo());
                        OrderItem deliveryOrderItem = deliveryPlanOrderItems.get(0);
                        updateStock(deliveryOrderItem.getSku(), cancelOrder.getAreaNo(), deliveryPlan.getQuantity(), merchant.getMname(), SaleStockChangeTypeEnum.CANCEL_ORDER, orderNo, recordMap,bigMerchant,deliveryPlan.getContactId());
                        skus.add(deliveryOrderItem.getSku());
                    }
                }
                if (!CollectionUtils.isEmpty(skus)) {
                    PurchasesConfig purchasesConfig = new PurchasesConfig();
                    purchasesConfig.setAreaNo(cancelOrder.getAreaNo());
                    purchasesConfig.setSkus(skus);
                    asyncTaskService.purchasesArrival(purchasesConfig);
                }
            //}
            quantityChangeRecordService.insert(recordMap);
        }

        //省心送退活动库存
        if (OrderTypeEnum.TIMING.getId().equals(cancelOrder.getType())) {
            log.info("省心送订单取消,返还活动库存,orderNo:{}", orderNo);
            //处理sku顺序、避免死锁
            orderItems.sort(Comparator.comparing(OrderItem::getSku));
            for (OrderItem orderItem : orderItems) {
                activityService.orderCancelActivityQuantity(orderNo,orderItem.getSku(),merchant.getmId());
            }
            //省心送订单退款删除
            timingOrderRefundTimeMapper.deleteByOrderNo(orderNo);
        }
        //大客户返还预付
        if(Objects.equals(Global.BIG_MERCHANT,merchant.getSize())){
            List<PrepayInventoryRecord> prepayInventoryRecords = prepayInventoryRecordMapper.selectRecordListByOrderNo(orderNo);
            if(!CollectionUtils.isEmpty(prepayInventoryRecords)){
                prepayInventoryRecords.forEach( x->{
                    boolean decrease = prepayInventoryService.increase(merchant.getAdminId(), x.getSku(), orderNo, x.getAmount());
                    if(!decrease){
                        throw new DefaultServiceException(1,"取消订单返还预约商品数量失败");
                    }
                });
            }
        }
        //同时普通 订单 把配送计划取消掉
        DeliveryPlan updateKeys = new DeliveryPlan();
        updateKeys.setMasterOrderNo(orderNo);
        updateKeys.setOrderNo(orderNo);
        updateKeys.setStatus(OrderStatusEnum.CANCEL.getId());
        deliveryPlanMapper.updateStatus(updateKeys);
        //取消订单项
        orderItemMapper.updateStatusByOrderNo(orderNo, 11);
        // 更新支付记录为取消
        Payment paymentSelectKey = new Payment();
        paymentSelectKey.setOrderNo(orderNo);
        Payment unpayment = paymentMapper.selectOne(paymentSelectKey);
        if (unpayment != null) {
            paymentMapper.updateStatusById(unpayment.getPaymentId(),11);
        }
        return true;
    }

    @Override
    public List<OrderCancelItem> unpaidOrders() {
        List<Orders> unpaidOrders = ordersMapper.unpaidOrders(OrderStatusEnum.NO_PAYMENT.getId(), null);
        if (CollectionUtils.isEmpty(unpaidOrders)) {
            log.info("无待支付订单");
            return new ArrayList<>();
        }
        return unpaidOrders.stream()
                .map(orders -> {
                    Map selectKeys = new HashMap();
                    selectKeys.put("mId", orders.getmId());
                    Merchant merchant = merchantMapper.selectOne(selectKeys);
                    log.info("加载订单{}至取消订单延迟队列", orders.getOrderNo());
                    OrderCancelItem orderCancelItem=new OrderCancelItem("cancelorder" + orders.getOrderNo(),
                            DateUtils.date2LocalDateTime(orders.getOrderTime()),
                            30 * 60 * 1000L,
                            merchant.getmId(),
                            merchant.getOpenid(),
                            merchant.getAreaNo(),
                            orders.getOrderNo());

                    DelayData delayData = new DelayData();
                    delayData.setType(MType.ORDER_TIMEOUT_CLOSE.name());
                    delayData.setData(JSONObject.toJSONString(orderCancelItem));
//                    producer.sendDelayDataToQueue(MQTopicConstant.MALL_DELAY_LIST, JSONObject.toJSONString(delayData), MQDelayConstant.SIXTEEN_DELAY_LEVEL);
                    mqProducer.sendDelay(MQTopicConstant.MALL_DELAY_LIST, null, JSONObject.toJSONString(delayData), 30*60*1000L);
                    return orderCancelItem;
                }).collect(Collectors.toList());
    }

    @Override
    public synchronized Orders createDiscountCardOrder(Integer discountCardId) {
        MerchantSubject merchantSubject = RequestHolder.getMerchantSubject();
        Long mId = merchantSubject.getMerchantId();

        if(RequestHolder.isMajor()){
            throw new DefaultServiceException(0, ResultConstant.DEFAULT_FAILED);
        }

        DiscountCard discountCard = discountCardMapper.selectByPrimaryKey(discountCardId);
        if(discountCard == null){
            throw new DefaultServiceException(0, ResultConstant.PARAM_FAULT);
        }

        //查询是否存在待支付的订单，存在修改为已撤销后再发起
        Orders orders = ordersMapper.selectUnPayDiscountOrder(mId, discountCardId);
        if (orders != null) {
           return orders;
        }
        //创建订单号
        String orderNo = Global.createOrderNo(Global.VIRTUAL_GOODS_ORDER_CODE);
        //创建订单
        Orders order = orderServiceHelper.createOrder(merchantSubject, discountCard, orderNo);

        //订单延迟取消
        VirtualOrderCancelItem item = new VirtualOrderCancelItem("cancel_virtual_order" + orderNo, LocalDateTime.now(), 30 * 60 * 1000L, orderNo);
        try {
            DelayData delayData = new DelayData();
            delayData.setType(MType.VIRTUAL_ORDER_TIMEOUT_CLOSE.name());
            delayData.setData(JSONObject.toJSONString(item));
//            producer.sendDelayDataToQueue(MQTopicConstant.MALL_DELAY_LIST, JSONObject.toJSONString(delayData), MQDelayConstant.SIXTEEN_DELAY_LEVEL);
            mqProducer.sendDelay(MQTopicConstant.MALL_DELAY_LIST, null, JSONObject.toJSONString(delayData), 30*60*1000L);
        } catch (Exception e) {
            log.error("充值订单加入延时取消队列失败", e);
        }

        return order;
    }

    @Override
    public AjaxResult sumNoPayOrders(){
        Long merchantId = RequestHolder.getMId();
        int sumNoPay = ordersMapper.selectSumOrders(merchantId, OrderStatusEnum.NO_PAYMENT.getId());
        int sumDelivering = ordersMapper.selectSumOrders(merchantId, OrderStatusEnum.DELIVERING.getId());
        HashMap<String, Integer> resultMap = new HashMap<>();
        resultMap.put("sumNoPay",sumNoPay);
        resultMap.put("sumDelivering",sumDelivering);
        return AjaxResult.getOK(resultMap);
    }

    /**
     * 查看是否是茶百道门店下安佳淡奶油的订单
     * @param orderNo
     * @return
     */
    @Override
    public Boolean checkPush(String orderNo) {
        String adminIdStr = configService.getValue(Global.CBD_ADMIN_ID);
        CheckInput input = new CheckInput(Global.AJD_SKU,adminIdStr,orderNo);
        return ordersMapper.checkIsCBD(input);
    }

    @Override
    public Collection<? extends DelayQueueItem> unpaidVirtualOrders() {
        List<Orders> unpaidOrders = ordersMapper.unpaidVirtualOrders();
        if (CollectionUtils.isEmpty(unpaidOrders)) {
            log.info("无待支付虚拟商品订单");
            return new ArrayList<>();
        }
        return unpaidOrders.stream()
                .map(orders -> {
                    log.info("加载订单{}至取消订单延迟队列", orders.getOrderNo());
                    VirtualOrderCancelItem virtualOrderCancelItem = new VirtualOrderCancelItem("cancel_virtual_order" + orders.getOrderNo(),
                            DateUtils.date2LocalDateTime(orders.getOrderTime()),
                            30 * 60 * 1000L,
                            orders.getOrderNo());

                    DelayData delayData = new DelayData();
                    delayData.setType(MType.VIRTUAL_ORDER_TIMEOUT_CLOSE.name());
                    delayData.setData(JSONObject.toJSONString(virtualOrderCancelItem));
                    mqProducer.sendDelay(MQTopicConstant.MALL_DELAY_LIST, null, JSONObject.toJSONString(delayData), 30*60*1000L);
                    return virtualOrderCancelItem;
                }).collect(Collectors.toList());
    }

    @Override
    public void virtualTimeOutClose(VirtualOrderCancelItem delayQueueItem) {
        String orderNo = delayQueueItem.getOrderNo();
        //调用关单接口
        try {
            paymentHandler.closeOrder(orderNo);
        } catch (Exception e) {
            // 允许关单失败，记录异常即可
            log.error("关闭支付单{}失败", orderNo, e);
        }
        //1.在订单表中更新已撤销信息
        int rs = ordersMapper.updateStatus(orderNo, OrderStatusEnum.CANCEL.getId(), OrderStatusEnum.NO_PAYMENT.getId());
        if (rs != 1) {
            log.error("关闭虚拟商品订单{}失败，订单状态异常", orderNo);
            return;
        }

        // 更新支付记录为取消
        Payment paymentSelectKey = new Payment();
        paymentSelectKey.setOrderNo(orderNo);
        Payment paymentRec = paymentMapper.selectOne(paymentSelectKey);
        if (paymentRec != null) {
            paymentMapper.updateStatusById(paymentRec.getPaymentId(),11);
        }
        log.info("虚拟商品订单" + orderNo + "开始关单...");

    }

    @Override
    public void addFruitSales(String orderNo) {
        List<FruitSales> fruitSales = ordersMapper.selectIsFruit(orderNo);
        if (CollectionUtils.isEmpty(fruitSales)){
           return;
        }
        Orders orders = ordersMapper.selectByOrderNo(orderNo);
        for (FruitSales fruitSale : fruitSales) {
            Integer warehouseNo = fenceService.selectWarehouseNo(orders.getAreaNo(), fruitSale.getSku());
            fruitSale.setAreaNo(warehouseNo);
        }
        MQData mqData = new MQData();
        mqData.setType(MType.ADD_FRUIT_SALES.name());
        mqData.setData(fruitSales);
        mqProducer.send("stock-list",null, JSON.toJSONString(mqData));
    }

    @Override
    public void reduceFruitSales(Long orderItemId, Integer areaNo) {
        FruitSales fruitSales = ordersMapper.selectIsFruitByItemId(orderItemId);
        if (Objects.isNull(fruitSales)){
            return;
        }
        Integer warehouseNo = fenceService.selectWarehouseNo(areaNo, fruitSales.getSku());
        fruitSales.setAreaNo(warehouseNo);
        MQData mqData = new MQData();
        mqData.setType(MType.REDUCE_FRUIT_SALES.name());
        mqData.setData(fruitSales);
        mqProducer.send("stock-list",null, JSON.toJSONString(mqData));
    }

    /**
     * 处理水果销量问题
     * @param orderNo
     */
    @Override
    public void handleFruitSales(String orderNo) {
        List<FruitSales> fruitSales = ordersMapper.selectIsFruit(orderNo);
        if (CollectionUtils.isEmpty(fruitSales)){
            return;
        }
        Orders orders = ordersMapper.selectByOrderNo(orderNo);
        Integer areaNo = orders.getAreaNo();
        for (FruitSales fruitSale : fruitSales) {
            String sku = fruitSale.getSku();
            Integer warehouseNo = fenceService.selectWarehouseNo(areaNo, sku);
            if (Objects.isNull(warehouseNo)) {
                continue;
            }
            fruitSale.setAreaNo(warehouseNo);
        }
        MQData mqData = new MQData();
        mqData.setType(MType.ADD_FRUIT_SALES.name());
        mqData.setData(fruitSales);
        mqProducer.send("stock-list",null, JSON.toJSONString(mqData));
    }

    @Override
    public Boolean checkFirstOrderByMid(String orderNo, Long mId) {
        LocalDateTime endTime = LocalDateTime.now();
        LocalDateTime startTime = LocalDateTime.of(LocalDate.now(),LocalTime.MIN);
        return ordersMapper.hasOrderByMid(mId, orderNo, startTime, endTime);
    }

    @Override
    public Boolean checkDeliveryTime(List<Contact> contacts, LocalDate localDate, Long mId) {
        List<Long> contactIds = contacts.stream().map(Contact::getContactId).collect(Collectors.toList());
        return ordersMapper.hasDeliveryPlanByTime(contactIds, localDate);
    }

    @Override
    public AjaxResult getDeliveryPoint(DeliveryPointDTO deliveryPointDTO) {
        if(ObjectUtils.isEmpty(deliveryPointDTO) || StringUtils.isBlank(deliveryPointDTO.getOrderNo())){
            AjaxResult.getErrorWithMsg("订单参数为空");
        }
        Orders orders = ordersMapper.selectByOrderNo(deliveryPointDTO.getOrderNo());
        if (orders == null || OrderStatusEnum.DELIVERING.getId() != orders.getStatus()){
            return AjaxResult.getOK();
        }
        return deliveryService.getCurrentDriverLocation(deliveryPointDTO.getOrderNo(), QueryDeliverySourceEnum.MALL.getCode(),deliveryPointDTO.getContactId());
    }

    @Override
    public CommonResult<OrderDeliveryVO> getOrderDelivery() {
        List<OrderDeliveryDTO> deliveryDTOS =  ordersMapper.orderDeliveryList(RequestHolder.getMId(),LocalDate.now());
        if (CollectionUtils.isEmpty(deliveryDTOS)){
            return CommonResult.ok();
        }
        OrderDeliveryVO orderDeliveryVO = new OrderDeliveryVO();
        for (OrderDeliveryDTO orderDeliveryDTO : deliveryDTOS){
            AjaxResult ajaxResult =  deliveryService.getCurrentDriverLocation(orderDeliveryDTO.getOrderNo(), QueryDeliverySourceEnum.MALL.getCode(), orderDeliveryDTO.getContactId());
            if (ajaxResult.isSuccess()){
                orderDeliveryVO.setOrderNo(orderDeliveryDTO.getOrderNo());
                orderDeliveryVO.setContactId(orderDeliveryDTO.getContactId());
                orderDeliveryVO.setOrderTime(orderDeliveryDTO.getOrderTime());
                orderDeliveryVO.setOrderStatus(orderDeliveryDTO.getStatus());
                orderDeliveryVO.setOrderSaleType(orderDeliveryDTO.getOrderSaleType());
                orderDeliveryVO.setActive(orderDeliveryDTO.getStatus());
                //设置订单配送状态。
                List<DeliveryPlanVO> deliveryPlanVOS = deliveryPlanMapper.selectByOrderNoIntercept(orderDeliveryDTO.getOrderNo());
                if(!CollectionUtils.isEmpty(deliveryPlanVOS)){
                    orderServiceHelper.handleOrderDeliveryPlans(orderDeliveryVO, deliveryPlanVOS);
                }else {
                    orderDeliveryVO.setDeliveryStatus(DeliveryStatusEnum.NOT_YET.getStatus());
                }

                return CommonResult.ok(orderDeliveryVO);
            }
        }
        return CommonResult.ok();
    }

    @Override
    public Integer getUnfilledOrderByMid(Long mId) {
        return ordersMapper.getUnfilledOrderByMid(mId);
    }

    @Override
    public Boolean billExport(OrderBillExportReq orderBillExportReq) {
        //拼接时分秒 结束时间更换为23:59:59
        Calendar cal = Calendar.getInstance();
        cal.setTime(orderBillExportReq.getEndTime());
        cal.set(Calendar.HOUR_OF_DAY, 23);
        cal.set(Calendar.MINUTE, 59);
        cal.set(Calendar.SECOND, 59);
        cal.set(Calendar.MILLISECOND, 59);
        orderBillExportReq.setEndTime(cal.getTime());

        //校验当前门店类型是否为单店且为现结账单
        Long mId = RequestHolder.getMId();
        orderBillExportReq.setMId(mId);
        Merchant merchant = merchantMapper.selectByPrimaryKey(mId);
        if (Objects.equals(merchant.getSize(), MerchantSizeEnum.MAJOR_CUSTOMER.getValue()) &&
                Objects.equals(merchant.getDirect(), PayTypeEnum.BILL.getType())) {
            log.warn("OrderServiceImpl[]billExport[]orderBillExportReq:{}", JSONObject.toJSONString(orderBillExportReq));
            throw new BizException("当前只支持单店且为现结类型账单数据导出");
        }

        //根据条件查询数据 普通订单和省心送订单
        Map<String, Object> params = getOrdersParams(orderBillExportReq);
        int commonCount = orderItemMapper.getCommonOrderExportListCount(params);
        int timingCount = orderItemMapper.getTimingOrderExportListCount(params);
        if (commonCount == 0 && timingCount == 0) {
            log.warn("OrderServiceImpl[]billExport[]orderBillExportReq:{}", JSONObject.toJSONString(orderBillExportReq));
            throw new BizException("当前时间范围内暂无可导出的订单数据");
        }

        //保存订单导出记录
        OrderExportRecord orderExportRecord = new OrderExportRecord();
        orderExportRecord.setEmail(orderBillExportReq.getMail());
        orderExportRecord.setMId(mId);
        orderExportRecord.setParam(JSONObject.toJSONString(orderBillExportReq));
        orderExportRecordMapper.insertSelective(orderExportRecord);
        orderBillExportReq.setOrderExportRecordId(orderExportRecord.getId());
        orderBillExportReq.setMName(merchant.getMname());

        //异步导出订单信息
        MQData payData = new MQData();
        payData.setType(MType.ORDER_BILL_EXPORT.name());
        payData.setData(JSONObject.toJSONString(orderBillExportReq));
        mqProducer.send(RocketMqMessageConstant.MANAGE_LIST, null, JSONObject.toJSONString(payData));
        return Boolean.TRUE;
    }

    @Override
    public void orderBillExportAndSendMail(OrderBillExportReq orderBillExportReq) {
        //根据条件查询数据 普通订单和省心送订单
        Map<String, Object> params = getOrdersParams(orderBillExportReq);


        //查询普通订单数据-按照sku维度
        List<OrderItemVO> commonOrders = orderItemMapper.getCommonOrderExportList(params);

        //查询省心送订单数据-按照配送计划维度
        List<OrderItemVO> timingOrders = orderItemMapper.getTimingOrderExportList(params);

        //组装导出数据excel
        String mailTitle = orderBillExportReq.getMName() + "账单" + DateUtils.dateToString(orderBillExportReq.getStartTime())
                + "-" + DateUtils.dateToString(orderBillExportReq.getEndTime());
        Workbook workbook = new HSSFWorkbook();

        Font titleFont = workbook.createFont();
        titleFont.setFontName("宋体");
        titleFont.setFontHeightInPoints((short) 12);
        titleFont.setBold(Boolean.TRUE);

        Font cellFont = workbook.createFont();
        cellFont.setFontName("宋体");
        CellStyle cellStyle = workbook.createCellStyle();
        cellStyle.setFont(cellFont);
        cellStyle.setAlignment(HorizontalAlignment.FILL);
        cellStyle.setVerticalAlignment(VerticalAlignment.CENTER);

        Sheet commonSheet = workbook.createSheet("普通订单");
        int commonRowIndex = 0;
        Row commonTitle = commonSheet.createRow(commonRowIndex++);
        String[] commonTitleName = {"客户ID", "客户名称", "配送地址", "订单号", "下单时间", "配送时间", "订单状态", "商品sku",
                "商品名称", "规格重量", "商品数量", "实付单价", "商品实付总价", "配送费", "退款金额", "实付总额"};
        for (int i = 0; i < commonTitleName.length; i++) {
            Cell cell = commonTitle.createCell(i);
            cell.setCellValue(commonTitleName[i]);
        }

        Sheet timingSheet = workbook.createSheet("省心送");
        int timingRowIndex = 0;
        Row timingTitle = timingSheet.createRow(timingRowIndex++);
        String[] timingTitleName = {"客户ID", "客户名称", "配送地址", "订单号", "下单时间", "配送时间", "配送状态", "商品sku",
                "商品名称", "规格重量", "商品数量", "实付单价", "商品实付总价", "配送费", "退款金额", "实付总额"};
        for (int i = 0; i < timingTitleName.length; i++) {
            Cell cell = timingTitle.createCell(i);
            cell.setCellValue(timingTitleName[i]);
        }

        Row row;
        if (!CollectionUtils.isEmpty(commonOrders)) {
            Set<String> orderNos = new HashSet<>();
            for (OrderItemVO commonOrder : commonOrders) {
                row = commonSheet.createRow(commonRowIndex++);

                //运费计算 相同订单号只计算到第一个订单号上面
                BigDecimal deliveryFee;

                //获取配送计划
                List<DeliveryPlanVO> deliveryPlanVOS = deliveryPlanMapper.selectByOrderNo(commonOrder.getOrderNo());
                if (!CollectionUtils.isEmpty(deliveryPlanVOS)) {
                    DeliveryPlanVO deliveryPlanVO = deliveryPlanVOS.get(0);
                    StringBuilder address = new StringBuilder();
                    address.append(deliveryPlanVO.getProvince()).append(deliveryPlanVO.getCity()).append(deliveryPlanVO.getArea()).append(deliveryPlanVO.getAddress());
                    if (StringUtils.isNotBlank(deliveryPlanVO.getHouseNumber())) {
                        address.append(deliveryPlanVO.getHouseNumber());
                    }
                    row.createCell(2).setCellValue(address.toString());
                    row.createCell(5).setCellValue(deliveryPlanVO.getDeliveryTime().toString());
                }
                row.createCell(0).setCellValue(commonOrder.getmId());
                row.createCell(1).setCellValue(commonOrder.getmName());
                row.createCell(3).setCellValue(commonOrder.getOrderNo());
                row.createCell(4).setCellValue(DateUtils.localDateTimeToString(commonOrder.getOrderTime()));
                row.createCell(6).setCellValue(OrderStatusEnum.getEnum(commonOrder.getStatus()).getStatusName());
                row.createCell(7).setCellValue(commonOrder.getSku());
                row.createCell(8).setCellValue(commonOrder.getPdName());
                row.createCell(9).setCellValue(commonOrder.getWeight());
                row.createCell(10).setCellValue(commonOrder.getAmount());
                row.createCell(11).setCellValue(commonOrder.getPrice().toString());
                row.createCell(12).setCellValue(commonOrder.getActualTotalPrice().toString());
                if (orderNos.contains(commonOrder.getOrderNo())) {
                    deliveryFee = new BigDecimal("0.00");
                } else {
                    deliveryFee = commonOrder.getDeliveryFee();

                    //计算 运费优惠券抵扣费用
                    Map query = new HashMap();
                    query.put("orderNo", commonOrder.getOrderNo());
                    List<MerchantCouponVO> couponVOS = ordersCouponMapper.select(query);
                    if (!CollectionUtils.isEmpty(couponVOS)) {
                        for (MerchantCouponVO e : couponVOS) {
                            if (Objects.equals(e.getAgioType(), CouponEnum.CouponTypeEnum.DELIVERY.getCode())) {
                                deliveryFee = deliveryFee.subtract(e.getMoney());
                            }
                        }
                    }

                    //防止运费出现负数
                    if (deliveryFee.compareTo(BigDecimal.ZERO) < 0) {
                        deliveryFee = new BigDecimal("0.00");
                    }
                }
                row.createCell(13).setCellValue(deliveryFee.toString());

                //查询退款数据 实付总额 = 商品实付总额 + 配送费 - 退款金额
                AfterSaleOrder afterSaleOrder = new AfterSaleOrder();
                afterSaleOrder.setOrderNo(commonOrder.getOrderNo());
                afterSaleOrder.setSku(commonOrder.getSku());
                afterSaleOrder.setmId(commonOrder.getmId());
                List<AfterSaleOrderVO> afterSaleOrderVOS = afterSaleOrderMapper.selectRefundSuccessInfo(afterSaleOrder);
                if (CollectionUtils.isEmpty(afterSaleOrderVOS)) {
                    row.createCell(14).setCellValue(new BigDecimal("0.00").toString());
                    row.createCell(15).setCellValue(commonOrder.getActualTotalPrice().add(deliveryFee).toString());
                } else {
                    BigDecimal sumHandleNum = afterSaleOrderVOS.stream().map(AfterSaleOrderVO::getHandleNum)
                            .reduce(BigDecimal.ZERO, BigDecimal::add);
                    row.createCell(14).setCellValue(sumHandleNum.toString());
                    BigDecimal total = commonOrder.getActualTotalPrice().add(deliveryFee);
                    row.createCell(15).setCellValue(total.subtract(sumHandleNum).toString());
                }
                orderNos.add(commonOrder.getOrderNo());
            }
        }
        if (!CollectionUtils.isEmpty(timingOrders)) {
            Set<String> orderNos = new HashSet<>();
            for (OrderItemVO timingOrder : timingOrders) {
                row = timingSheet.createRow(timingRowIndex++);

                //运费计算 相同订单号只计算到第一个订单号上面
                BigDecimal deliveryFee;

                row.createCell(0).setCellValue(timingOrder.getmId());
                row.createCell(1).setCellValue(timingOrder.getmName());
                StringBuilder address = new StringBuilder();
                address.append(timingOrder.getProvince()).append(timingOrder.getCity()).append(timingOrder.getArea()).append(timingOrder.getAddress());
                if (StringUtils.isNotBlank(timingOrder.getHouseNumber())) {
                    address.append(timingOrder.getHouseNumber());
                }
                row.createCell(2).setCellValue(address.toString());
                row.createCell(3).setCellValue(timingOrder.getOrderNo());
                row.createCell(4).setCellValue(DateUtils.localDateTimeToString(timingOrder.getOrderTime()));
                row.createCell(5).setCellValue(timingOrder.getDeliveryTime().toString());
                row.createCell(6).setCellValue(OrderStatusEnum.getEnum(timingOrder.getDeliveryStatus()).getStatusName());
                row.createCell(7).setCellValue(timingOrder.getSku());
                row.createCell(8).setCellValue(timingOrder.getPdName());
                row.createCell(9).setCellValue(timingOrder.getWeight());
                row.createCell(10).setCellValue(timingOrder.getQuantity());
                row.createCell(11).setCellValue(timingOrder.getPrice().toString());
                BigDecimal actualTotalPrice = timingOrder.getPrice().multiply(new BigDecimal(timingOrder.getQuantity()));
                row.createCell(12).setCellValue(actualTotalPrice.toString());
                if (orderNos.contains(timingOrder.getOrderNo())) {
                    deliveryFee = new BigDecimal("0.00");
                } else {
                    deliveryFee = timingOrder.getDeliveryFee();
                }
                row.createCell(13).setCellValue(deliveryFee.toString());

                //查询退款数据 实付总额 = 商品实付总额 + 配送费 - 退款金额
                AfterSaleOrder afterSaleOrder = new AfterSaleOrder();
                afterSaleOrder.setOrderNo(timingOrder.getOrderNo());
                afterSaleOrder.setSku(timingOrder.getSku());
                afterSaleOrder.setmId(timingOrder.getmId());
                afterSaleOrder.setDeliveryId(timingOrder.getDeliveryId());
                List<AfterSaleOrderVO> afterSaleOrderVOS = afterSaleOrderMapper.selectRefundSuccessInfo(afterSaleOrder);
                if (CollectionUtils.isEmpty(afterSaleOrderVOS)) {
                    row.createCell(14).setCellValue(new BigDecimal("0.00").toString());
                    row.createCell(15).setCellValue(actualTotalPrice.add(deliveryFee).toString());
                } else {
                    BigDecimal sumHandleNum = afterSaleOrderVOS.stream().map(AfterSaleOrderVO::getHandleNum)
                            .reduce(BigDecimal.ZERO, BigDecimal::add);
                    row.createCell(14).setCellValue(sumHandleNum.toString());
                    BigDecimal total = actualTotalPrice.add(deliveryFee);
                    row.createCell(15).setCellValue(total.subtract(sumHandleNum).toString());
                }
                orderNos.add(timingOrder.getOrderNo());
            }
        }


        //发送邮件
        Map<String, Workbook> bodyPart = new HashMap<>();
        bodyPart.put(mailTitle + ".xls", workbook);
        OrderExportRecord orderExportRecord = new OrderExportRecord();
        try {
            log.info("OrderServiceImpl[]orderBillExportAndSendMail[]sendMail[]orderBillExportReq:{}", JSONObject.toJSONString(orderBillExportReq));
            mailUtil.sendMailAndExcel(mailTitle, null, orderBillExportReq.getMail().split(","), null, bodyPart);
            orderExportRecord.setStatus(OrderExportStatusEnum.SUCCESS.getCode());
        } catch (Exception e) {
            orderExportRecord.setStatus(OrderExportStatusEnum.FAIL.getCode());
            log.error("OrderServiceImpl[]orderBillExportAndSendMail[]sendMail[]error[]cause:{}", JSONObject.toJSONString(e));
        }

        //修改记录状态
        orderExportRecord.setId(orderBillExportReq.getOrderExportRecordId());
        orderExportRecordMapper.updateByPrimaryKeySelective(orderExportRecord);
    }

    @Override
    public boolean isNotWarehouseIntoOrder(String orderNo) {
        List<OrderItemVO> orderItemVOS = orderItemMapper.selectOrderItemVO(orderNo);
        return orderItemVOS.stream().anyMatch(el -> InventoryEnums.SubType.SELF_NOT_INTO_WAREHOUSE.getSubType().equals(el.getSubType()));
    }

    @Override
    @InMemoryCache(expiryTimeInSeconds = 1 * 60)
    public TimingRuleVO getTimingRuleVOByCache(Integer timingRuleId) {
        return timingRuleMapper.selectByPrimaryKey(timingRuleId);
    }

    /**
     * @description: 组装入参信息
     * @author: lzh
     * @date: 2023/6/8 14:11
     * @param: [orderBillExportReq]
     * @return: java.util.Map<java.lang.String,java.lang.Object>
     **/
    private Map<String, Object> getOrdersParams(OrderBillExportReq orderBillExportReq) {
        Map<String, Object> params = new HashMap();
        params.put("mId", orderBillExportReq.getMId());
        List<Integer> status = new ArrayList();
        status.add(OrderStatusEnum.DELIVERING.getId());
        status.add(OrderStatusEnum.RECEIVED.getId());
        params.put("status", status);
        List<Integer> types = new ArrayList();
        types.add(OrderTypeEnum.NORMAL.getId());
        types.add(OrderTypeEnum.TIMING.getId());
        types.add(OrderTypeEnum.DELIVERY_FEE.getId());
        types.add(OrderTypeEnum.HELP.getId());
        params.put("types", types);
        params.put("orderPayType", PayTypeEnum.CASH.getType());
        params.put("startTime", orderBillExportReq.getStartTime());
        params.put("endTime", orderBillExportReq.getEndTime());
        return params;
    }

    private void getOrderItemInfo(List<OrderItemVO> orderItemVOS) {
        orderItemVOS.forEach(el -> {
            //组装订单快照信息
            OrderItemInfoDTO orderItemInfoDTO;
            if (StringUtils.isNotBlank(el.getInfo()) && el.getInfo().contains("{") && el.getInfo().contains("}")){
                try {
                    orderItemInfoDTO = JSON.parseObject(el.getInfo(), OrderItemInfoDTO.class);
                    el.setOrderItemInfoDTO(orderItemInfoDTO);
                } catch (Exception e) {
                    log.warn("解析商品快照信息失败，orderItemVO:{}", JSON.toJSONString(el));
                    //兼容老逻辑 之前逻辑info只存储了有效期快照
                    orderItemInfoDTO = new OrderItemInfoDTO();
                    orderItemInfoDTO.setValidity(el.getInfo());
                    el.setOrderItemInfoDTO(orderItemInfoDTO);
                }
            } else if (StringUtils.isNotBlank(el.getInfo())) {
                //兼容老逻辑 之前逻辑info只存储了有效期快照
                orderItemInfoDTO = new OrderItemInfoDTO();
                orderItemInfoDTO.setValidity(el.getInfo());
                el.setOrderItemInfoDTO(orderItemInfoDTO);
            }
        });
    }
}
