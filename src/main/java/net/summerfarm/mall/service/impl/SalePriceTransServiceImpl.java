package net.summerfarm.mall.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.cofso.item.client.constant.MarketConstant;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.common.base.Throwables;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import net.summerfarm.common.exceptions.DefaultServiceException;
import net.summerfarm.contexts.ResultConstant;
import net.summerfarm.enums.InventoryAdjustPriceTypeEnum;
import net.summerfarm.mall.common.util.PageInfoHelper;
import net.summerfarm.mall.common.util.RedisCacheUtil;
import net.summerfarm.mall.common.util.RequestHolder;
import net.summerfarm.mall.constant.Constants;
import net.summerfarm.mall.contexts.Global;
import net.summerfarm.mall.engine.client.req.EsHomeProductQueryConditionReq;
import net.summerfarm.mall.engine.client.req.EsHomeProductQueryReq;
import net.summerfarm.mall.enums.CommodityTopEnum;
import net.summerfarm.mall.enums.CommonStatus;
import net.summerfarm.mall.enums.MajorPriceTypeEnum;
import net.summerfarm.mall.enums.DistOrderSourceEnum;
import net.summerfarm.mall.enums.ProductCategoryTypeEnum;
import net.summerfarm.mall.enums.market.ActivityTypeEnum;
import net.summerfarm.mall.enums.market.ScopeTypeEnum;
import net.summerfarm.mall.facade.engine.MarketQueryProviderFacade;
import net.summerfarm.mall.facade.engine.model.EsMarketItemInfoDTO;
import net.summerfarm.mall.mapper.*;
import net.summerfarm.mall.model.domain.*;
import net.summerfarm.mall.model.domain.market.ActivitySkuDetail;
import net.summerfarm.mall.model.dto.inventory.MarketItemDTO;
import net.summerfarm.mall.model.dto.market.activity.ActivityInfoDTO;
import net.summerfarm.mall.model.dto.market.activity.ActivityScopeDTO;
import net.summerfarm.mall.model.dto.market.activity.ActivitySkuDTO;
import net.summerfarm.mall.model.dto.market.activity.ActivitySkuDetailDTO;
import net.summerfarm.mall.model.dto.product.MallProductQueryDTO;
import net.summerfarm.mall.model.dto.product.MarketProviderQueryDTO;
import net.summerfarm.mall.model.dto.product.ProductInventoryInfoDTO;
import net.summerfarm.mall.model.vo.*;
import net.summerfarm.mall.model.vo.product.HomeProductQueryVo;
import net.summerfarm.mall.service.*;
import net.summerfarm.mall.service.convert.ProductConvert;
import net.summerfarm.mall.service.facade.MarketItemFacade;
import net.summerfarm.mall.service.facade.ProductServiceFacade;
import net.summerfarm.mall.service.facade.WmsAreaStoreFacade;
import net.summerfarm.mall.service.facade.converter.MarketItemConverter;
import net.summerfarm.mall.service.facade.dto.AreaStoreQueryReq;
import net.summerfarm.mall.service.facade.dto.AreaStoreQueryRes;
import net.summerfarm.mall.service.facade.dto.MarketItemDetailDTO;
import net.summerfarm.mall.service.facade.dto.ProductExpireReqDTO;
import net.summerfarm.mall.service.utils.PriceCalculator;
import net.summerfarm.wnc.client.enums.SourceEnum;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.ibatis.annotations.Param;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static net.summerfarm.mall.model.vo.ProductInfoVO.isFruitCategoryType;

@Service
public class SalePriceTransServiceImpl implements SalePriceTransService {

    public static final Logger log = LoggerFactory.getLogger(SalePriceTransServiceImpl.class);

    @Lazy
    @Resource
    ActivityService activityService;
    @Resource
    InventoryMapper inventoryMapper;
    @Resource
    MerchantService merchantService;
    @Resource
    InventoryService inventoryService;
    @Resource
    FrontCategoryService frontCategoryService;
    @Resource
    private PriceStrategyServiceImpl priceStrategyService;
    @Resource
    AreaSkuMapper areaSkuMapper;
    @Resource
    private FrontCategoryConfigMapper frontCategoryConfigMapper;
    @Resource
    private ActivitySkuDetailMapper activitySkuDetailMapper;
    @Resource
    private ActivityBasicInfoMapper activityBasicInfoMapper;
    @Resource
    private ProductServiceFacade productServiceIntegration;
    @Resource
    private FenceService fenceService;
    @Resource
    private RedisCacheUtil redisCacheUtil;
    @Resource
    private CategoryService categoryService;
    @Resource
    private AreaSkuService areaSkuService;
    @Resource
    private MarketQueryProviderFacade marketQueryProviderFacade;
    @Resource
    private MajorPriceMapper majorPriceMapper;
    @Resource
    private ContactService contactService;
    @Resource
    private WmsAreaStoreFacade wmsAreaStoreFacade;
    @Resource
    private BrandMapper brandMapper;
    @Resource
    private CategoryMapper categoryMapper;
    @Resource
    private MarketItemFacade marketItemFacade;
    @Resource
    private ProductSearchRerankService productSearchRerankService;
    @Resource
    private MarketPriceControlProductsService marketPriceControlProductsService;
    @Resource
    private FrequentSkuPoolService frequentSkuPoolService;

    @Override
    public PageInfo<ProductInfoVO> selectHomeProductVO2(Integer frontCategoryId, Integer areaNo,
                                                        List<EsProductVO> esProductVOS, Integer adminId, List<String> skuList,
                                                        Integer skuShow, Integer show, @Param(value = "direct") Integer direct,
                                                        String msize, String queryStr, Boolean coreCustomer, PageVo pageVo, Integer type, Integer helpOrderFlag) {
        Fence fence = fenceService.selectCacheOneByAreaNo(areaNo);
        if (fence == null || fence.getStoreNo() == null) {
            return new PageInfo<>();
        }
        List<Integer> categoryIds = null;
        if (frontCategoryId != null) {
            categoryIds = redisCacheUtil.getDataWithCacheList(Global.CACHE_FRONT_CATEGORY + frontCategoryId, 300, Integer.class, () -> frontCategoryService.selectCategoryId(frontCategoryId));
        }
        Integer storeNo = fence.getStoreNo();
        if (pageVo != null) {
            PageHelper.startPage(pageVo.getPageIndex(), pageVo.getPageSize());
        }
        List<ProductInfoVO> productInfoVOS = inventoryMapper.selectHomeProductVO(categoryIds, areaNo, storeNo, esProductVOS,
            adminId, skuList, skuShow, show, direct, msize, queryStr, coreCustomer, type, helpOrderFlag);
        List<String> skus = productInfoVOS.stream().map(ProductInfoVO::getSku).collect(Collectors.toList());
        if (skus.isEmpty()) {
            return new PageInfo<>();
        }
        // 查询价格信息
        List<AreaSku> areaSkuList = areaSkuMapper.selectAreaSkuByAreaNoAndSkus(areaNo, skus);
        Map<String, AreaSku> areaSkuMap = areaSkuList.stream().collect(Collectors.toMap(AreaSku::getSku, Function.identity()));
        Map<String, MajorPrice> majorPriceMap = Maps.newHashMap();
        if (RequestHolder.isMajor()) {
            List<MajorPrice> majorPriceList = majorPriceMapper.selectMajorPriceList(adminId, direct, areaNo, skus);
            if (!CollectionUtils.isEmpty(majorPriceList)) {
                majorPriceMap = majorPriceList.stream().collect(Collectors.toMap(MajorPrice::getSku, Function.identity()));
            }
        }
        handleSalePriceOrMajorPrice(productInfoVOS, areaSkuMap, majorPriceMap);
        handleShelfLife(productInfoVOS, areaNo);
        return PageInfoHelper.createPageInfo(productInfoVOS);
    }

    @Override
    public void handleSalePrice(Map<String, ActivitySkuDetailDTO> activitySkuDetailMap, ProductInfoVO productInfo) {
        //大客户不需要查询特价
        if (RequestHolder.isMajor()) {
            return;
        }
        ActivitySkuDetailDTO activitySku = activitySkuDetailMap.get(productInfo.getSku());
        if (activitySku != null) {
            BigDecimal salePrice = BigDecimal.valueOf(productInfo.getSalePrice());
            productInfo.setLadderPrice(null);
            productInfo.setActivityLadderPrices(activitySku.getLadderPrices());
            productInfo.setActivityLimitedQuantity(activitySku.getLimitQuantity());
            Integer limitQuantity = activityService.getActivityLimitQuantity(activitySku);
            //设置了限购，remainQuantity肯定是取到限购数量
            if (activitySku.getAccountLimit() > 0) {
                productInfo.setRemainingQuantity(limitQuantity);
            }
            if (activitySku.getActivityPrice() != null) {
                //获取活动库存
                if (limitQuantity > 0) {
                    productInfo.setSalePrice(activitySku.getActivityPrice().doubleValue());
                    productInfo.setPlatform(activitySku.getPlatform());
                } else {
                    productInfo.setLadderPrices(null);
                }
            }

            //活动为人群包的话需要实时计算阶梯价格
            if (Objects.equals(activitySku.getIsCrowdPack(), CommonStatus.YES.getCode())) {
                activitySku.getLadderPrices().forEach(ladderPriceVO -> {
                    //需要单独计算活动价格
                    PriceStrategy priceStrategy = new PriceStrategy();
                    priceStrategy.setAmount(ladderPriceVO.getAmount());
                    priceStrategy.setAdjustType(ladderPriceVO.getAdjustType());
                    priceStrategy.setRoundingMode(ladderPriceVO.getRoundingMode());
                    BigDecimal costPrice = null;
                    if (Objects.equals(InventoryAdjustPriceTypeEnum.GROSS_PROFIT_PERCENTAGE.getType(), priceStrategy.getAdjustType())) {
                        costPrice = inventoryService.selectCostPriceByCache(productInfo.getSku(), RequestHolder.getMerchantAreaNo());
                    }
                    BigDecimal activityPrice = priceStrategyService.calcStrategyPrice(priceStrategy, costPrice, salePrice);
                    ladderPriceVO.setPrice(activityPrice);
                });
            }
        }
    }


    private void handleProductAvailableQuantity(List<ProductInfoVO> productInfoVOS) {
        if (CollectionUtils.isEmpty(productInfoVOS)) {
            return;
        }
        boolean isCoreCustomer = merchantService.checkCoreCustomers(RequestHolder.getNewMerchantId());

        //未获取到预留库存最大值则认为已经处理过了
        for (ProductInfoVO productInfoVO : productInfoVOS) {
            //优先取SKU名称，如果为空，则兜底为SPU名称+SKU规格信息
            productInfoVO.resetSkuNameAndSkuPic();
            if (!isCoreCustomer && !Objects.isNull(productInfoVO.getReserveMaxQuantity())) {
                Integer singleQuantity = inventoryService.getSingleQuantity(productInfoVO);
                productInfoVO.setQuantity(singleQuantity);
            }
        }
    }
    @Override
    public List<ProductInfoVO> selectProductInfoByPdIdV2(long pdId, Integer areaNo, Integer adminId, List<String> skuList, Integer skuShow, Integer direct) {
        Fence fence = fenceService.selectCacheOneByAreaNo(areaNo);
        if (fence == null || fence.getStoreNo() == null) {
            log.warn("areaNo:{}对应的围栏的信息不存在", areaNo);
            return Lists.newArrayList();
        }
        boolean coreCustomer = merchantService.checkCoreCustomers(RequestHolder.getNewMerchantId());
        List<ProductInfoVO> productInfoVOS = selectRPCProductInfoByPdId(pdId, areaNo, adminId, skuList, skuShow, direct, coreCustomer);
        if (CollectionUtils.isEmpty(productInfoVOS)) {
            return productInfoVOS;
        }

        Map<String, ActivitySkuDetailDTO> activitySkuDetailMap = Maps.newHashMap();
        if (!RequestHolder.isMajor()) {//NOSONAR
            //批量查活动sku
            List<ActivitySkuDTO> activitySkuDTOS = new ArrayList<>(productInfoVOS.size());
            productInfoVOS.forEach(productInfoVO -> {
                ActivitySkuDTO activitySkuDTO = new ActivitySkuDTO();
                activitySkuDTO.setSku(productInfoVO.getSku());
                activitySkuDTOS.add(activitySkuDTO);
            });
            activitySkuDetailMap = activityService.listByActivitySku(
                    activitySkuDTOS, areaNo, RequestHolder.getMId());

        }

        for (ProductInfoVO productInfoVO : productInfoVOS) {
            //优先取SKU名称，如果为空，则兜底为SPU名称+SKU规格信息
            productInfoVO.resetSkuNameAndSkuPic();
            String sku = productInfoVO.getSku();
            productInfoVO.setActivitySku(false);
            fillAvgInfo(productInfoVO);
            BigDecimal salePrice = BigDecimal.valueOf(productInfoVO.getSalePrice());
            //大客户跳过活动查询
            if (RequestHolder.isMajor()) {
                continue;
            }

            ActivitySkuDetailDTO activitySku = null;
            if (CollectionUtil.isNotEmpty(activitySkuDetailMap) && activitySkuDetailMap.containsKey(sku)) {
                activitySku = activitySkuDetailMap.get(sku);
            }
            //如果是特价商品，需要校验限购
            if (activitySku != null) {
                productInfoVO.setActivityLadderPrices(activitySku.getLadderPrices());
                productInfoVO.setIsSupportTiming(activitySku.getIsSupportTiming() == null ? CommonStatus.NO.getCode()
                        : activitySku.getIsSupportTiming());
                productInfoVO.setActivityLimitedQuantity(activitySku.getLimitQuantity());
                productInfoVO.setActivityActualQuantity(activitySku.getActualQuantity());

                //获取活动库存
                Integer remainQuantity = activityService.getActivityLimitQuantity(activitySku);

                //设置了限购，remainQuantity肯定是取到限购数量
                if (activitySku.getAccountLimit() > 0) {
                    productInfoVO.setRemainingQuantity(remainQuantity);
                }

                if (activitySku.getActivityPrice() != null) {
                    productInfoVO.setActivitySku(true);

                    //还未达活动可购买数量
                    if (remainQuantity > 0) {
                        productInfoVO.setSalePrice(activitySku.getActivityPrice().doubleValue());
                        productInfoVO.setPlatform(activitySku.getPlatform());
                        productInfoVO.setCostPrice(productInfoVO.getPrice());
                    }
                }

                //活动为人群包的话需要实时计算阶梯价格
                if (Objects.equals(activitySku.getIsCrowdPack(), CommonStatus.YES.getCode())) {
                    activitySku.getLadderPrices().forEach(ladderPriceVO -> {
                        //需要单独计算活动价格
                        PriceStrategy priceStrategy = new PriceStrategy();
                        priceStrategy.setAmount(ladderPriceVO.getAmount());
                        priceStrategy.setAdjustType(ladderPriceVO.getAdjustType());
                        priceStrategy.setRoundingMode(ladderPriceVO.getRoundingMode());
                        BigDecimal costPrice = null;
                        if (Objects.equals(InventoryAdjustPriceTypeEnum.GROSS_PROFIT_PERCENTAGE.getType(), priceStrategy.getAdjustType())) {
                            costPrice = inventoryService.selectCostPriceByCache(sku, areaNo);
                        }
                        BigDecimal activityPrice = priceStrategyService.calcStrategyPrice(priceStrategy, costPrice, salePrice);
                        ladderPriceVO.setPrice(activityPrice);
                    });
                }
            }
        }
        handleShelfLife(productInfoVOS, areaNo);
        fillPackagingMaterial(productInfoVOS);
        return productInfoVOS;
    }

    private void fillPackagingMaterial(List<ProductInfoVO> productInfoVOS) {

        Map<String, String> labelMap = new HashMap<> ();
        Set<String> skus = productInfoVOS.stream ().map (ProductInfoVO::getSku).collect (Collectors.toSet ());
        List<MarketItemDetailDTO> marketItemDetailDTOS = marketItemFacade.queryMarketItemLabelByItemCodes (skus);
        if(CollectionUtil.isNotEmpty (marketItemDetailDTOS)){
                marketItemDetailDTOS = marketItemDetailDTOS.stream ().filter (itemDetail -> StringUtils.isNotBlank (itemDetail.getItemLabel ())).collect(Collectors.toList());
                if(CollectionUtils.isNotEmpty (marketItemDetailDTOS)) {
                    labelMap = marketItemDetailDTOS.stream()
                            .collect(Collectors.toMap(
                                    MarketItemDetailDTO::getItemCode,  // 键: itemCode
                                    MarketItemDetailDTO::getItemLabel, // 值: itemLabel
                                    (existing, replacement) -> replacement // 如果键重复，保留最后一个值
                            ));
                }
        }
        Map<String, String> finalLabelMap = labelMap;
        productInfoVOS.forEach (productInfoVO -> {
            if(finalLabelMap.containsKey (productInfoVO.getSku ()) && finalLabelMap.get (productInfoVO.getSku ()).contains ("包材")){
                productInfoVO.setPackagingMaterial(true);
            }else{
                productInfoVO.setPackagingMaterial(false);
            }
        });
    }

    private List<ProductInfoVO> selectRPCProductInfoByPdId(long pdId, Integer areaNo, Integer adminId,List<String> skuList, Integer skuShow, Integer direct, boolean coreCustomer) {
        log.info("开始查询商品信息：pdId:{}, areaNo:{}, adminId:{}, skuList:{}, skuShow:{}, direct:{}, coreCustomer:{}", pdId, areaNo, adminId, JSON.toJSONString(skuList), skuShow, direct, coreCustomer);
        // 查询商品
        PageInfo<MarketItemDTO> marketItemDTOPageInfo = marketItemFacade.listMarketItem(MarketProviderQueryDTO.builder()
            .pdId(pdId)
            .areaNo(areaNo)
            .skus(skuList)
            .adminShow(skuShow)
            .sortDescList(Collections.singletonList(MarketConstant.ORDERBY_ON_SALE_SHOW))
            .build());
        List<MarketItemDTO> marketItemList = marketItemDTOPageInfo.getList();
        if (CollectionUtils.isEmpty(marketItemList)) {
            log.warn("商品信息为空!");
            return Collections.emptyList();
        }
        List<String> marketItemSkuList = new ArrayList<>();
        List<Integer> brandIds = new ArrayList<>();
        List<Integer> categoryIds = new ArrayList<>();
        for (MarketItemDTO item : marketItemList) {
            marketItemSkuList.add(item.getItemCode());
            Optional.ofNullable(item.getBrandId()).ifPresent(id -> brandIds.add(id.intValue()));
            Optional.ofNullable(item.getCategoryId()).ifPresent(id -> categoryIds.add(id.intValue()));
        }


        // 查询 major_price
        Map<String, MajorPrice> majorPriceMap = new HashMap<>();
        if (Objects.nonNull(adminId)) {
            List<MajorPrice> majorPrices = majorPriceMapper.selectSkusMajorPriceList(adminId, direct, areaNo, marketItemSkuList);
            majorPriceMap = majorPrices.stream().collect(Collectors.toMap(MajorPrice::getSku, Function.identity(), (k1, k2) -> k2));
        }

        // 查询area_store
        Long mId = RequestHolder.getMId();
        Map<String, AreaStoreQueryRes> areaStoreMap = getAreaStoreMap(mId, marketItemSkuList);

        // 查询inventory中剩余字段
        List<ProductInventoryInfoDTO> inventoryDtos = inventoryMapper.selectProductInvInfo(marketItemSkuList, areaNo);
        Map<String, ProductInventoryInfoDTO> inventoryDtoMap = inventoryDtos.stream().collect(Collectors.toMap(ProductInventoryInfoDTO::getSku, Function.identity(), (k1, k2) -> k1));

        // 查询brand
        Map<Integer, String> brandNameMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(brandIds)) {
            List<Brand> brands = brandMapper.selectByIds(brandIds);
            brandNameMap = brands.stream().collect(Collectors.toMap(Brand::getBrandId, Brand::getName));
        }

        // 查询category
        Map<Integer, Integer> categoryTypeMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(categoryIds)) {
            List<Category> categories = categoryMapper.selectByIds(categoryIds);
            categoryTypeMap = categories.stream().collect(Collectors.toMap(Category::getId, Category::getType));
        }

        Map<String, MajorPrice> finalMajorPriceMap = majorPriceMap;
        Map<Integer, String> finalBrandNameMap = brandNameMap;
        Map<Integer, Integer> finalCategoryTypeMap = categoryTypeMap;
        Map<String, AreaStoreQueryRes> finalAreaStoreMap = areaStoreMap;
        return marketItemList.stream()
            .map(i -> buildProductInfo(finalAreaStoreMap, inventoryDtoMap, finalMajorPriceMap, finalBrandNameMap, finalCategoryTypeMap, i, coreCustomer))
            .collect(Collectors.toList());
    }


    private ProductInfoVO buildProductInfo(Map<String, AreaStoreQueryRes> areaStoreMap,
                                           Map<String, ProductInventoryInfoDTO> inventoryDtoMap,
                                           Map<String, MajorPrice> majorPriceMap,
                                           Map<Integer, String> brandNameMap,
                                           Map<Integer, Integer> categoryTypeMap,
                                           MarketItemDTO marketItemDTO, boolean coreCustomer) {
        AreaStoreQueryRes areaStore = areaStoreMap.getOrDefault(marketItemDTO.getItemCode(), new AreaStoreQueryRes());
        ProductInventoryInfoDTO inventoryDto = inventoryDtoMap.getOrDefault(marketItemDTO.getItemCode(), new ProductInventoryInfoDTO());
        MajorPrice majorPrice = majorPriceMap.getOrDefault(marketItemDTO.getItemCode(), new MajorPrice());
        String brandName = null;
        if (Objects.nonNull(marketItemDTO.getBrandId())){
            brandName = brandNameMap.getOrDefault(marketItemDTO.getBrandId().intValue(), null);
        }
        int categoryType = categoryTypeMap.getOrDefault(marketItemDTO.getCategoryId().intValue(), ProductCategoryTypeEnum.ALL.getCode());

        /**
         * 已废弃字段：
         * area_sku.info
         * inventory.introduction
         * inventory.production_date
         */
        ProductInfoVO productInfoVO = MarketItemConverter.convert2ProductVO(marketItemDTO);

        productInfoVO.setDirect(majorPrice.getDirect());


//        Optional.ofNullable(majorPrice.getPrice()).ifPresent(p -> {
//            productInfoVO.setPrice(p.doubleValue());
//        });
        if(!Objects.equals (majorPrice.getPriceType (), MajorPriceTypeEnum.MALL_PRICE.getCode ())){
            BigDecimal price = PriceCalculator.calculateMajorPriceByType (majorPrice.getPrice(), BigDecimal.valueOf (inventoryDto.getSalePrice()), majorPrice.getPriceAdjustmentValue (), majorPrice.getPriceType ());
            productInfoVO.setPrice(price.doubleValue ());
        }

        /*// 特殊处理商城价
        log.info("开始处理大客户商城价：majorPrice：{}", JSON.toJSONString(majorPrice));
        if (Objects.equals(0, majorPrice.getPriceType()) && majorPrice.getAdminId() != null && majorPrice.getAreaNo() != null && majorPrice.getSku() != null) {
            BigDecimal mallPrice = majorPriceService.getMajorMallPrice(majorPrice.getAreaNo(), majorPrice.getSku(), majorPrice.getAdminId(), RequestHolder.getMId());
            if(null != mallPrice) {
                productInfoVO.setPrice(mallPrice.doubleValue());
            }
        }*/

        Optional.ofNullable(areaStore.getCostPrice()).ifPresent(p -> {
            productInfoVO.setCostPrice(p.doubleValue());
        });
        // 设置默认库存,防止库存泄露,实际库存数据从其他接口获取
        // 获取当前区域店铺的在线可售库存,如果为空则默认为0
        int skuOnlineQuantity = Optional.ofNullable(areaStore.getOnlineQuantity()).orElse(0);

        // 判断库存是否异常:
        // 1. 在线库存小于等于0
        // 2. 或基础销售数量(购买基数)乘以基础销售单位(最小购买单位)大于在线库存
        if (0 >= skuOnlineQuantity || productInfoVO.getBaseSaleQuantity() * productInfoVO.getBaseSaleUnit() > skuOnlineQuantity) {
            // 库存异常时,设置默认展示库存值
            productInfoVO.setOnlineQuantity(Constants.AreaStoreConstant.defaultOnlineQuantity);
            productInfoVO.setQuantity(Constants.AreaStoreConstant.defaultOnlineQuantity);
        } else {
            // 库存正常时,设置固定展示库存值
            productInfoVO.setOnlineQuantity(Constants.ONLINE_QUANTITY);
            productInfoVO.setQuantity(Constants.ONLINE_QUANTITY);
        }

        Optional.ofNullable(inventoryDto.getOriginalPrice()).ifPresent(p -> {
            productInfoVO.setActivityOriginPrice(new BigDecimal(p));
        });
        productInfoVO.setPdId(inventoryDto.getPdId());
        productInfoVO.setMaturity(inventoryDto.getMaturity());
        productInfoVO.setQualityTime(inventoryDto.getQualityTime());
        productInfoVO.setQualityTimeUnit(inventoryDto.getQualityTimeUnit());
        productInfoVO.setLadderPrice(inventoryDto.getLadderPriceStr());
        productInfoVO.setStorageMethod(inventoryDto.getStorageMethod());
        productInfoVO.setOriginalPrice(inventoryDto.getOriginalPrice());
        productInfoVO.setSalePrice(inventoryDto.getSalePrice());
        productInfoVO.setSlogan(inventoryDto.getSlogan());
        productInfoVO.setOtherSlogan(inventoryDto.getOtherSlogan());
        productInfoVO.setAveragePriceFlag(inventoryDto.getAveragePriceFlag());
        productInfoVO.setSalesMode(inventoryDto.getSalesMode());
        productInfoVO.setLimitedQuantity(inventoryDto.getLimitedQuantity());

        productInfoVO.setCateType(categoryType);
        productInfoVO.setType(inventoryDto.getType());// 理论上这个才是type：0，自营，1，代仓.
        productInfoVO.setBrandName(brandName);
        productInfoVO.setBuyerName (inventoryDto.getBuyerName ());
        productInfoVO.setBuyerId (inventoryDto.getBuyerId ());
        productInfoVO.setNetWeightNum (inventoryDto.getNetWeightNum ());
        productInfoVO.setWeightNum (inventoryDto.getWeightNum ());
        productInfoVO.setNetWeightUnit (inventoryDto.getNetWeightUnit());
        productInfoVO.setQuoteType (inventoryDto.getQuoteType());
        productInfoVO.setSubType(inventoryDto.getSubType());
        return productInfoVO;
    }

    @Override
    public PageInfo<ProductInfoVO> selectCouponProductVOV3(List<Integer> categoryIds, String pdName, Integer adminId,
                                                           List<String> skuList, Boolean coreCustomer,
                                                           List<String> blackSkus, PageVo pageVo) {
        MerchantSubject merchantSubject = RequestHolder.getMerchantSubject();
        //获取登录信息为空
        if (merchantSubject == null) {
            throw new DefaultServiceException(0, ResultConstant.LOGIN_FIRST);
        }

        Integer areaNo = RequestHolder.getMerchantAreaNo();
        Integer skuShow = merchantSubject.getSkuShow();
        Integer direct = merchantSubject.getDirect();

        Fence fence = fenceService.selectCacheOneByAreaNo(areaNo);
        if (fence == null || fence.getStoreNo() == null) {
            return new PageInfo<>();
        }

        EsHomeProductQueryConditionReq conditionReq = new EsHomeProductQueryConditionReq();
        //只有首页处理小程序过滤部分类目 默认false
        conditionReq.setMiniProgramLogin(false);
        conditionReq.setCoreCustomer(coreCustomer);
        conditionReq.setMajor(RequestHolder.isMajor());
        conditionReq.setSkuShow(skuShow);

        EsHomeProductQueryReq esHomeProductQueryReq = new EsHomeProductQueryReq();
        esHomeProductQueryReq.setAreaNo(areaNo);
        esHomeProductQueryReq.setShow(1);
        esHomeProductQueryReq.setBlackListSku(blackSkus);
        esHomeProductQueryReq.setCategoryIds(categoryIds);
        esHomeProductQueryReq.setPdName(pdName);
        if (skuShow == null && !CollectionUtils.isEmpty(skuList)) {
            esHomeProductQueryReq.setSkuList(skuList);
        }
        if (RequestHolder.isMajor()) {
            if (!CollectionUtils.isEmpty(skuList)) {
                esHomeProductQueryReq.setMajorSkuList(skuList);
            }
            if (Objects.equals(skuShow, 2)) {
                //大客户专享过滤 报价单sku 和 非大客户专享sku
                esHomeProductQueryReq.setMType(0);
            }
        }
        esHomeProductQueryReq.setPageIndex(pageVo.getPageIndex());
        esHomeProductQueryReq.setPageSize(pageVo.getPageSize());

        try {
            PageInfo<EsMarketItemInfoDTO> esMarketItemPageInfo = marketQueryProviderFacade.getCouponMarketItemEsInfoEntity(esHomeProductQueryReq, conditionReq);
            try (Page<ProductInfoVO> productInfoVOS = new Page<>(pageVo.getPageIndex(), pageVo.getPageSize())) {
                for (EsMarketItemInfoDTO esMarketItemInfoDTO : esMarketItemPageInfo.getList()) {
                    ProductInfoVO productInfoVO = EsMarketItemInfoDTO.convertToProductInfoVO(esMarketItemInfoDTO, coreCustomer);
                    productInfoVOS.add(productInfoVO);
                }
                if (CollectionUtils.isEmpty(productInfoVOS)) {
                    return PageInfo.emptyPageInfo();
                }
                productInfoVOS.setTotal(esMarketItemPageInfo.getTotal());
                List<String> skus = productInfoVOS.stream().map(ProductInfoVO::getSku).collect(Collectors.toList());
                // 查询价格信息
                List<AreaSku> areaSkuList = areaSkuMapper.selectAreaSkuByAreaNoAndSkus(areaNo, skus);
                Map<String, AreaSku> areaSkuMap = areaSkuList.stream().collect(Collectors.toMap(AreaSku::getSku, Function.identity()));
                Map<String, MajorPrice> majorPriceMap = Maps.newHashMap();
                if (RequestHolder.isMajor()) {
                    List<MajorPrice> majorPriceList = majorPriceMapper.selectSkusMajorPriceList(adminId, direct, areaNo, skus);
                    if (!CollectionUtils.isEmpty(majorPriceList)) {
                        majorPriceMap = majorPriceList.stream().collect(Collectors.toMap(MajorPrice::getSku,Function.identity(),(existing, replacement) -> existing));
                    }
                }
                handleSalePriceOrMajorPrice(productInfoVOS, areaSkuMap, majorPriceMap);
                // 实时查询商品保质期
                handleShelfLife(productInfoVOS, areaNo);
                //新增标签
                return productInfoVOS.toPageInfo();
            }
        } catch (Exception e) {
            log.error("es商品列表查询异常", e);
            return PageInfo.emptyPageInfo();
        }

    }

    /**
     * 查询推荐商品VO列表.
     * <p>
     *     根据区域编号和SKU列表查询推荐商品信息，并根据是否为POP商家执行不同的逻辑。
     *     如果区域编号对应的围栏信息不存在或店铺编号为空，则返回空列表。
     * </p>
     *
     * @param areaNo 区域编号
     * @param skuList SKU列表
     * @return 推荐商品VO列表, 如果没有推荐商品或围栏信息异常，则返回空列表
     */
    @Override
    public List<ProductInfoVO> selectRecommendProductVO(Integer areaNo, List<String> skuList) {
        Fence fence = fenceService.selectCacheOneByAreaNo(areaNo);
        if (fence == null || fence.getStoreNo() == null) {
            return Collections.emptyList();
        }

        List<ProductInfoVO> productInfoVOS;
        if (RequestHolder.isPopMerchant()) {
            productInfoVOS = inventoryMapper.selectRecommendProductVOForPOP(areaNo, fence.getStoreNo(), skuList);
            Optional.ofNullable(productInfoVOS).ifPresent(productInfoList -> {
                productInfoList.forEach(this::fillAvgWeight); // POP商城特殊的逻辑：填充单位质量单价，比如5.0元/斤
            });
        } else {
            productInfoVOS = inventoryMapper.selectRecommendProductVO(areaNo, fence.getStoreNo(), skuList);
        }

        handleProductAvailableQuantity(productInfoVOS);
        handleShelfLife(productInfoVOS, areaNo);
        Optional.ofNullable(productInfoVOS).ifPresent(productInfoList -> {
            productInfoList.forEach(ProductInfoVO::resetSkuNameAndSkuPic); // 主要是需要修改SKU的名字。
        });
        return productInfoVOS;
    }

    @Override
    public List<ProductInfoVO> activityRecommendProduct(Integer areaNo, List<String> skuList) {
        Fence fence = fenceService.selectCacheOneByAreaNo(areaNo);
        if (fence == null || fence.getStoreNo() == null) {
            return new ArrayList<>();
        }
        List<ProductInfoVO> productInfoVOS = inventoryMapper.selectRecommendProductVO(areaNo, fence.getStoreNo(), skuList);
        handleProductAvailableQuantity(productInfoVOS);
        return productInfoVOS;
    }

    @Override
    public void handleProductShelfLife(List<ProductInfoVO> productInfoVOS, Integer areaNo) {
        handleShelfLife(productInfoVOS, areaNo);
    }

    @Override
    public PageInfo<ProductInfoVO> selectRecommendProductVOV3(Integer areaNo, List<String> skuList, Integer pageNum, Integer pageSize) {
        Fence fence = fenceService.selectCacheOneByAreaNo(areaNo);
        if (fence == null || fence.getStoreNo() == null) {
            return PageInfo.emptyPageInfo();
        }

        EsHomeProductQueryConditionReq conditionReq = new EsHomeProductQueryConditionReq();
        boolean isCoreCustomer = merchantService.checkCoreCustomers(RequestHolder.getNewMerchantId());
        conditionReq.setCoreCustomer(isCoreCustomer);
        conditionReq.setMajor(false);
        //小程序过滤部分类目、仅展示自营品
        conditionReq.setMiniProgramLogin(false);

        EsHomeProductQueryReq esHomeProductQueryReq = new EsHomeProductQueryReq();
        esHomeProductQueryReq.setAreaNo(areaNo);
        if (!CollectionUtils.isEmpty(skuList)) {
            esHomeProductQueryReq.setSkuList(skuList);
        }
        esHomeProductQueryReq.setPageIndex(pageNum);
        esHomeProductQueryReq.setPageSize(pageSize);
        try {
            PageInfo<EsMarketItemInfoDTO> esMarketItemPageInfo = marketQueryProviderFacade.getHomeCommonRecommendMarketItemEsInfoEntity(esHomeProductQueryReq, conditionReq);
            if (Objects.isNull(esMarketItemPageInfo) || CollectionUtils.isEmpty(esMarketItemPageInfo.getList())) {
                return PageInfo.emptyPageInfo();
            }
            try {

                PageInfo<ProductInfoVO> productVoPage = ProductConvert.convert2ProductVO(esMarketItemPageInfo);
                List<ProductInfoVO> productInfoVOS = esMarketItemPageInfo.getList().stream()
                    .map(i -> EsMarketItemInfoDTO.convertToProductInfoVO(i, isCoreCustomer))
                    .collect(Collectors.toList());

                List<String> skus = productInfoVOS.stream().map(ProductInfoVO::getSku).collect(Collectors.toList());
                // 查询价格信息
                List<AreaSku> areaSkuList = areaSkuMapper.selectAreaSkuByAreaNoAndSkus(areaNo, skus);
                Map<String, AreaSku> areaSkuMap = areaSkuList.stream().collect(Collectors.toMap(AreaSku::getSku, Function.identity()));
                handleSalePriceOrMajorPrice(productInfoVOS, areaSkuMap, Collections.emptyMap());
                handleProductAvailableQuantity(productInfoVOS);
                handleShelfLife(productInfoVOS, areaNo);

                productVoPage.setList(productInfoVOS);
                return productVoPage;
            } catch (Exception e) {
                log.error("推荐接口es查询异常", e);
                return PageInfo.emptyPageInfo();
            }
        } catch (Exception e) {
            log.error("推荐接口es查询异常", e);
            return PageInfo.emptyPageInfo();
        }
    }

    @Override
    public List<ProductInfoVO> selectGroupBuyProductVO(Integer areaNo, List<String> skuList) {
        List<ProductInfoVO> productInfoVOS = inventoryMapper.selectHomeProductVOBySkus(areaNo, skuList);
        handleProductAvailableQuantity(productInfoVOS);
        return productInfoVOS;
    }

    /**
     * 根据区域编号和商品列表更新商品的保质期信息。
     * 此方法从服务端获取商品的保质期数据，并更新每个商品的相关信息。
     * 对于水果类商品，不展示保质期信息。
     * 若在获取数据过程中发生错误，将记录警告日志，但不会影响商城商品的正常展示。
     *
     * @param productInfoVOS 包含需要更新保质期信息的商品列表
     * @param areaNo 表示区域编号的整数值
     */
    private void handleShelfLife(List<ProductInfoVO> productInfoVOS, Integer areaNo) {
        //保质期异常不能影响到商城商品展示
        List<String> allSkuList = new ArrayList<>();
        try {
            Long merchantId = RequestHolder.getMId();
            if (merchantId == null) {
                log.warn("RequestHolder.getMId()返回空的mId，无法获取商品保质期");
                return;
            }
            Contact contact = contactService.getMerchantDefaultContactCache(merchantId);
            if (contact == null) {
                log.warn("mId:{}没有默认联系人，无法获取保质期", merchantId);
                return;
            }
            List<String> nonFruitSkuList = productInfoVOS.stream().filter(productVO -> {
                        allSkuList.add(productVO.getSku());
                        productVO.setInfo("");// 先统一设置为空字符
                        return !isFruitCategoryType(productVO);// 只需要获取非鲜果的SKU的保质期，其他类型不需要。
                    }).map(ProductInfoVO::getSku)
                    .collect(Collectors.toList());
            if (CollectionUtils.isEmpty(nonFruitSkuList)) {
                log.info("这一批SKU全都是水果，不需要获取保质期:{}", allSkuList);
                return;
            }
            ProductExpireReqDTO expireReqDTO = new ProductExpireReqDTO();
            expireReqDTO.setSkuIds(nonFruitSkuList);
            expireReqDTO.setMerchantId(merchantId);
            expireReqDTO.setContactId(contact.getContactId());
            expireReqDTO.setContact(contact);
            expireReqDTO.setSource(DistOrderSourceEnum.getDistOrderSource(RequestHolder.getBusinessLine()));
            Map<String, String> expireInfoList = productServiceIntegration.listProductExpireInfo(expireReqDTO);
            productInfoVOS.forEach(productInfoVO -> {
                productInfoVO.setInfo(expireInfoList.getOrDefault(productInfoVO.getSku(), ""));
                log.info("SKU:{}的保质期:{}", productInfoVO.getSku(), productInfoVO.getInfo());
            });
        } catch (Exception e) {
            log.warn("获取保质期异常,areaNo:{},allSkuList:{}, cause:{}", areaNo, allSkuList, Throwables.getStackTraceAsString(e));
        }
    }

    @Override
    public List<ProductInfoVO> selectLandPageFormulaProductVO(Integer areaNo, Long formulaItemId, Boolean coreCustomer) {
        List<ProductInfoVO> productInfoVOS = inventoryMapper.selectLandPageFormulaProductVO(areaNo, formulaItemId, coreCustomer);
        return productInfoVOS;
    }

    @Override
    public List<ProductInfoVO> selectLandPageSkuProductVO(Integer areaNo, Long productId) {
        List<ProductInfoVO> productInfoVOS = inventoryMapper.selectLandPageSkuProductVO(areaNo, productId);
        handleProductAvailableQuantity(productInfoVOS);
        handleShelfLife(productInfoVOS, areaNo);
        return productInfoVOS;
    }

    @Override
    public List<ProductInfoVO> selectLandPageSkuAutoSortProductVO(Integer areaNo, Long productId, Boolean coreCustomer) {
        List<ProductInfoVO> productInfoVOS = inventoryMapper.selectLandPageSkuAutoSortProductVO(areaNo, productId, coreCustomer);
        //优先取SKU名称，如果为空，则兜底为SPU名称+SKU规格信息
        productInfoVOS.forEach(x -> x.resetSkuNameAndSkuPic());
        handleShelfLife(productInfoVOS, areaNo);
        return productInfoVOS;
    }

    @Override
    public List<ProductInfoVO> selectExpiredSkuAutoSortProductVO(Integer areaNo) {
        Fence fence = fenceService.selectCacheOneByAreaNo(areaNo);
        if (fence == null || fence.getStoreNo() == null) {
            return Lists.newArrayList();
        }
        boolean coreCustomer = merchantService.checkCoreCustomers(RequestHolder.getNewMerchantId());
        List<ProductInfoVO> productInfoVOS = inventoryMapper.selectExpiredSkuAutoSortProductVO(areaNo, coreCustomer);
        //过滤出临保活动商品
        List<ActivityScopeDTO> scopes = Lists.newArrayList();
        scopes.add(new ActivityScopeDTO(areaNo.longValue(), ScopeTypeEnum.AREA.getCode()));

        //查询临保活动,一个用户肯定最多只会有一个临保活动，临保活动到运营城市维度
        List<ActivityInfoDTO> nearExpiredActivityInfoDTOS = activityBasicInfoMapper.selectByScope(
            scopes, Lists.newArrayList(ActivityTypeEnum.NEAR_EXPIRED.getCode()));
        if (CollectionUtils.isEmpty(nearExpiredActivityInfoDTOS)) {
            return Lists.newArrayList();
        }

        ActivityInfoDTO activityInfoDTO = nearExpiredActivityInfoDTOS.get(0);
        List<ActivitySkuDetail> skuDetails = activitySkuDetailMapper.selectByItemConfig(
            activityInfoDTO.getItemConfigId());
        if (CollectionUtils.isEmpty(skuDetails)) {
            return Lists.newArrayList();
        }
        List<String> skus = skuDetails.stream().map(x -> x.getSku()).distinct()
            .collect(Collectors.toList());
        List<ProductInfoVO> infoVOList = productInfoVOS.stream().filter(x -> skus.contains(x.getSku()) && x.getQuantity() > 0)
            .map(x -> x.resetSkuNameAndSkuPic()).sorted(Comparator.comparing(ProductInfoVO::getSortQuantity)
                .thenComparing(ProductInfoVO::getSales)).collect(
                Collectors.toList());
        handleShelfLife(infoVOList, areaNo);
        return infoVOList;
    }

    @Override
    public PageInfo<ProductInfoVO> selectRecommendProductVOV3(Integer areaNo, Map<String, Object> esSkuSortMap, List<String> fixSkus, PageVo pageVo) {
        Fence fence = fenceService.selectCacheOneByAreaNo(areaNo);
        if (fence == null || fence.getStoreNo() == null) {
            return new PageInfo();
        }
        EsHomeProductQueryReq esHomeProductQueryReq = new EsHomeProductQueryReq();
        EsHomeProductQueryConditionReq conditionReq = new EsHomeProductQueryConditionReq();
        boolean isCoreCustomer = merchantService.checkCoreCustomers(RequestHolder.getNewMerchantId());
        conditionReq.setCoreCustomer(isCoreCustomer);
        esHomeProductQueryReq.setAreaNo(areaNo);
        esHomeProductQueryReq.setMType(0);
        //小程序过滤部分类目、仅展示自营品
        conditionReq.setMiniProgramLogin(RequestHolder.isMiniProgramLogin());

//      如果是大客户 && 全量
        if (RequestHolder.isMajor() && Objects.equals(RequestHolder.getMerchantSubject().getSkuShow(), 2)) {
            esHomeProductQueryReq.setUnShowSkus ( inventoryService.mallShowHideSkuList (RequestHolder.getAdminId(), RequestHolder.getMerchantAreaNo(), RequestHolder.getDirect ()));
        }

        //小程序过滤部分类目
        if (RequestHolder.isMiniProgramLogin()) {
            Set<Integer> unShowCategoryId = categoryService.getUnShowCategoryId();
            if (CollectionUtil.isNotEmpty(unShowCategoryId)) {
                esHomeProductQueryReq.setMiniProgramCategoryIds(unShowCategoryId);
        }
            //大客户过滤代仓商品
            if (RequestHolder.isMajor()) {
                List<Integer> unShowAdminId = Lists.newArrayList(450, 744, 1110, 1175, 1227, 1525);
                if (unShowAdminId.contains(RequestHolder.getAdminId())) {
                    Set<String> unShowSku = areaSkuService.selectAgentSku(RequestHolder.getAdminId(), RequestHolder.getMerchantAreaNo());
                    if (CollectionUtil.isNotEmpty(unShowSku)) {
                        esHomeProductQueryReq.setMiniProgramUnShowSkus(unShowSku);
                    }
                }
            }
        }
        if (!CollectionUtils.isEmpty(fixSkus)) {
            esHomeProductQueryReq.setSkuList(fixSkus);
        }
        esHomeProductQueryReq.setEsSkuSortMap(esSkuSortMap);
        esHomeProductQueryReq.setPageIndex(pageVo.getPageIndex());
        esHomeProductQueryReq.setPageSize(pageVo.getPageSize());
        try {
            PageInfo<EsMarketItemInfoDTO> esMarketItemPageInfo = marketQueryProviderFacade.getRecommendMarketItemEsInfoEntity(esHomeProductQueryReq, conditionReq);
            if (Objects.isNull(esMarketItemPageInfo) || CollectionUtils.isEmpty(esMarketItemPageInfo.getList())) {
                return PageInfoHelper.createPageInfo(new ArrayList<>());
            }
            try (Page<ProductInfoVO> productInfoVOS = new Page<>(pageVo.getPageIndex(), pageVo.getPageSize())) {
                for (EsMarketItemInfoDTO esMarketItemInfoDTO : esMarketItemPageInfo.getList()) {
                    ProductInfoVO productInfoVO = EsMarketItemInfoDTO.convertToProductInfoVO(esMarketItemInfoDTO, isCoreCustomer);
                    productInfoVOS.add(productInfoVO);
                }
                if (CollectionUtils.isEmpty(productInfoVOS)) {
                    return PageInfoHelper.createPageInfo(new ArrayList<>());
                }
                productInfoVOS.setTotal(esMarketItemPageInfo.getTotal());
                if (CollectionUtils.isEmpty(productInfoVOS)) {
                    return PageInfoHelper.createPageInfo(new ArrayList<>());
                }
                List<String> skus = productInfoVOS.stream().map(ProductInfoVO::getSku).collect(Collectors.toList());
                // 查询价格信息
                List<ProductInfoVO> areaSkus = areaSkuMapper.selectByAreaNoAndSkus(areaNo, skus, null, null);
                for (ProductInfoVO productInfoVO : productInfoVOS) {
                    productInfoVO.resetSkuNameAndSkuPic();
//                    Map<String, ActivitySkuDetailDTO> finalActivitySkuDetailMap = activitySkuDetailMap;
                    areaSkus.stream().filter(item -> item.getSku().equals(productInfoVO.getSku()))
                        .findFirst()
                        .ifPresent(item -> {
                            String ladderPrice = item.getLadderPrice();
                            productInfoVO.setLadderPrice(ladderPrice);
                            if (StringUtils.isNotBlank(ladderPrice) || ObjectUtil.notEqual("[]", ladderPrice)) {
                                List<LadderPriceVO> ladderPriceObjs = JSONObject.parseArray(ladderPrice, LadderPriceVO.class);
                                productInfoVO.setLadderPrices(ladderPriceObjs);
                            }
                            productInfoVO.setSalePrice(item.getSalePrice());
                            productInfoVO.setOriginalPrice(item.getOriginalPrice());
                            productInfoVO.setType(item.getType());
                            productInfoVO.setExtType(item.getExtType());
                            productInfoVO.setQuoteType(item.getQuoteType());
                        });
                    //设置默认库存，防止库存泄露，库存从其他接口获取
                    if (productInfoVO.getBaseSaleQuantity() * productInfoVO.getBaseSaleUnit() > productInfoVO.getQuantity()) {
                        productInfoVO.setOnlineQuantity(Constants.AreaStoreConstant.defaultOnlineQuantity);
                        productInfoVO.setQuantity(Constants.AreaStoreConstant.defaultOnlineQuantity);
                    }else {
                        productInfoVO.setOnlineQuantity(Constants.ONLINE_QUANTITY);
                        productInfoVO.setQuantity(Constants.ONLINE_QUANTITY);
                    }
                    fillAvgInfo(productInfoVO);
                }
                handleShelfLife(productInfoVOS, areaNo);
                return productInfoVOS.toPageInfo();
            }
        } catch (Exception e) {
            log.error("推荐接口es查询异常", e);
            return PageInfoHelper.createPageInfo(new ArrayList<ProductInfoVO>());
        }
    }

    @Override
    public PageInfo<ProductInfoVO> selectHomeProductVO3(HomeProductQueryVo homeProductQueryVo, PageVo pageVo) {
        EsHomeProductQueryReq esHomeProductQueryReq = new EsHomeProductQueryReq();
        EsHomeProductQueryConditionReq conditionReq = new EsHomeProductQueryConditionReq();
        
        // 支持从接口传入areaNo，实现接口控制搜索某一特定区域的商品；
        Integer areaNo = Optional.ofNullable(homeProductQueryVo.getAreaNo()).orElse(RequestHolder.getMerchantAreaNo());
        Fence fence = fenceService.selectCacheOneByAreaNo(areaNo);
        if (fence == null || fence.getStoreNo() == null) {
            return new PageInfo<>();
        }
        esHomeProductQueryReq.setAreaNo(areaNo);
        MerchantSubject merchantSubject = homeProductQueryVo.getMerchantSubject();
        //小程序过滤部分类目
        conditionReq.setMiniProgramLogin(RequestHolder.isMiniProgramLogin());
        //小程序过滤部分类目
        if (RequestHolder.isMiniProgramLogin()) {
            Set<Integer> unShowCategoryId = categoryService.getUnShowCategoryId();
            if (CollectionUtil.isNotEmpty(unShowCategoryId)) {
                esHomeProductQueryReq.setMiniProgramCategoryIds(unShowCategoryId);
            }
            //大客户过滤代仓商品
            if (RequestHolder.isMajor()) {
                List<Integer> unShowAdminId = Lists.newArrayList(450, 744, 1110, 1175, 1227, 1525);
                if (unShowAdminId.contains(RequestHolder.getAdminId())) {
                    Set<String> unShowSku = areaSkuService.selectAgentSku(RequestHolder.getAdminId(), RequestHolder.getMerchantAreaNo());
                    if (CollectionUtil.isNotEmpty(unShowSku)) {
                        esHomeProductQueryReq.setMiniProgramUnShowSkus(unShowSku);
                    }
                }
            }
        }
        if (CollectionUtil.isNotEmpty(homeProductQueryVo.getSubTypeList())) {
            esHomeProductQueryReq.setSubTypeList(homeProductQueryVo.getSubTypeList());
        }

        Integer frontCategoryId = homeProductQueryVo.getFrontCategoryId();
        if (frontCategoryId != null) {
            //首页搜索页不再关心sku是否展示，所有sku平铺展示；分类页需要关心sku展示
            esHomeProductQueryReq.setFrontCategoryId(frontCategoryId);
            Integer show = homeProductQueryVo.getShow();
            if (show != null) {
                esHomeProductQueryReq.setShow(show);
            }
            List<Integer> categoryIds = frontCategoryService.selectCategoryId(frontCategoryId);
            if (!CollectionUtils.isEmpty(categoryIds)) {
                esHomeProductQueryReq.setCategoryIds(categoryIds);
            }
        }
        List<String> skuList = homeProductQueryVo.getSkuList();
        if (merchantSubject.getSkuShow() == null && !CollectionUtils.isEmpty(skuList)) {
            //terms
            esHomeProductQueryReq.setSkuList(skuList);
        }
        conditionReq.setMajor(RequestHolder.isMajor());
        conditionReq.setSkuShow(merchantSubject.getSkuShow());
        if (RequestHolder.isMajor()) {
            if (!CollectionUtils.isEmpty(skuList)) {
                //报价单sku
                esHomeProductQueryReq.setMajorSkuList(homeProductQueryVo.getSkuList());
            }
            //全量
            if (Objects.equals(merchantSubject.getSkuShow(), 2)) {
                esHomeProductQueryReq.setUnShowSkus (homeProductQueryVo.getNotShowSkuList ());
                esHomeProductQueryReq.setMType(0);
            }
        } else {
            //单店查询非大客户专享
            esHomeProductQueryReq.setMType(0);
        }

        if (StringUtils.isNotEmpty(homeProductQueryVo.getPdName())) {
            esHomeProductQueryReq.setPdName(homeProductQueryVo.getPdName());
        }
        List<String> topSkuList = null;
        Long mId = homeProductQueryVo.getMId();
        Long merchantId = Objects.isNull(mId) ? merchantSubject.getMerchantId() : mId;
        boolean coreCustomer = merchantService.checkCoreCustomers(merchantId);
        conditionReq.setCoreCustomer(coreCustomer);
        if (frontCategoryId != null) {
            topSkuList = frontCategoryConfigMapper.selectSku(homeProductQueryVo.getMerchantSubject().getArea().getLargeAreaNo(), frontCategoryId);
            if (!CollectionUtils.isEmpty(topSkuList)) {
                Map<String, Object> esSkuSortMap = new HashMap<>();
                esSkuSortMap = buildEsSkuSortMap(topSkuList, null);
                esHomeProductQueryReq.setEsSkuSortMap(esSkuSortMap);
            }
        }
        MallProductQueryDTO mallProductQueryDTO = null;
        if (null != merchantId && null != homeProductQueryVo.getDeliveryTime()) {
            mallProductQueryDTO = new MallProductQueryDTO(merchantId, homeProductQueryVo.getDeliveryTime());
        }
        esHomeProductQueryReq.setSkuQueryList(homeProductQueryVo.getSkuQueryList());
        esHomeProductQueryReq.setPageIndex(pageVo.getPageIndex());
        esHomeProductQueryReq.setPageSize(pageVo.getPageSize());
        try {
            // 增加搜索AB实验的支持：
            PageInfo<EsMarketItemInfoDTO> esMarketItemPageInfo = productSearchRerankService.searchProducts(esHomeProductQueryReq, conditionReq, mallProductQueryDTO,false);
            // PageInfo<EsMarketItemInfoDTO> esMarketItemPageInfo = marketQueryProviderFacade.homeProductQuery(esHomeProductQueryReq, conditionReq);
            if (Objects.isNull(esMarketItemPageInfo) || CollectionUtils.isEmpty(esMarketItemPageInfo.getList())) {
                return PageInfoHelper.createPageInfo(Collections.emptyList());
            }
            try (Page<ProductInfoVO> productInfoVOS = new Page<>(pageVo.getPageIndex(), pageVo.getPageSize())) {
                for (EsMarketItemInfoDTO esMarketItemInfoDTO : esMarketItemPageInfo.getList()) {
                    ProductInfoVO productInfoVO = EsMarketItemInfoDTO.convertToProductInfoVO(esMarketItemInfoDTO, coreCustomer);
                    productInfoVOS.add(productInfoVO);
                }
                if (CollectionUtils.isEmpty(productInfoVOS)) {
                    return PageInfoHelper.createPageInfo(Collections.emptyList());
                }
                productInfoVOS.setTotal(esMarketItemPageInfo.getTotal());
                List<String> skus = productInfoVOS.stream().map(ProductInfoVO::getSku).collect(Collectors.toList());
                // 查询价格信息
                List<ProductInfoVO> areaSkus = areaSkuMapper.selectByAreaNoAndSkus(areaNo, skus, homeProductQueryVo.getAdminId(), homeProductQueryVo.getDirect());
                for (ProductInfoVO productInfoVO : productInfoVOS) {
                    //优先取SKU名称，如果为空，则兜底为SPU名称+SKU规格信息
                    productInfoVO.resetSkuNameAndSkuPic();
                    if (!CollectionUtils.isEmpty(topSkuList)) {
                        if (topSkuList.contains(productInfoVO.getSku())) {
                            productInfoVO.setSortTop(CommodityTopEnum.SORT_TOP_UP.getId());
                        }
                    }
                    areaSkus.stream().filter(item -> item.getSku().equals(productInfoVO.getSku()))
                        .findFirst()
                        .ifPresent(item -> {
                            String ladderPrice = item.getLadderPrice();
                            productInfoVO.setLadderPrice(ladderPrice);
                            if (org.apache.commons.lang3.StringUtils.isNotBlank(ladderPrice) || ObjectUtil.notEqual("[]", ladderPrice)) {
                                List<LadderPriceVO> ladderPriceObjs = JSONObject.parseArray(ladderPrice, LadderPriceVO.class);
                                productInfoVO.setLadderPrices(ladderPriceObjs);
                            }
                            productInfoVO.setSalePrice(item.getSalePrice());
                            productInfoVO.setOriginalPrice(item.getOriginalPrice());
                            productInfoVO.setType(item.getType());
                            productInfoVO.setCateType(item.getCateType());
                            productInfoVO.setExtType(item.getExtType());
                            productInfoVO.setMallShow(item.getMallShow());
                            productInfoVO.setDirect(item.getDirect());
                            productInfoVO.setActivityOriginPrice(item.getActivityOriginPrice());
                            productInfoVO.setPrice(item.getPrice());
                            productInfoVO.setQuoteType(item.getQuoteType());

                        });
                    //设置默认库存，防止库存泄露，库存从其他接口获取
                    if (productInfoVO.getBaseSaleQuantity() * productInfoVO.getBaseSaleUnit() > productInfoVO.getQuantity()) {
                        productInfoVO.setOnlineQuantity(Constants.AreaStoreConstant.defaultOnlineQuantity);
                        productInfoVO.setQuantity(Constants.AreaStoreConstant.defaultOnlineQuantity);
                    }else {
                        productInfoVO.setOnlineQuantity(Constants.ONLINE_QUANTITY);
                        productInfoVO.setQuantity(Constants.ONLINE_QUANTITY);
                    }
                    fillAvgInfo(productInfoVO);
                }
                // 实时查询商品保质期
                handleShelfLife(productInfoVOS, areaNo);

                //获取全部控价品信息
                Map<String, MarketPriceControlProducts> controlProductsMap = marketPriceControlProductsService.selectAllControlProductsByCache();
                productInfoVOS.forEach(productInfoVO -> {
                    if (CollectionUtil.isEmpty(controlProductsMap) || !controlProductsMap.containsKey(productInfoVO.getSku())) {
                        return;
                    }
                    MarketPriceControlProducts marketPriceControlProducts = controlProductsMap.get(productInfoVO.getSku());
                    if (marketPriceControlProducts != null) {
                        productInfoVO.setPriceHide(marketPriceControlProducts.getPriceHide());
                        productInfoVO.setFacePriceHide(marketPriceControlProducts.getFacePriceHide());
                    }
                });

                // 查询是否在常购清单中
                Map<String, Boolean> skuInFrequentSkuPoolInfoMap = frequentSkuPoolService.checkSkuInFrequentSkuPool(merchantId, skus);
                productInfoVOS.forEach(productInfoVO -> {
                    productInfoVO.setInMerchantFrequentSkuPool(skuInFrequentSkuPoolInfoMap.getOrDefault(productInfoVO.getSku(), false));
                });
                //新增标签
                return productInfoVOS.toPageInfo();
            }
        } catch (Exception e) {
            log.error("es商品列表查询异常", e);
            return PageInfoHelper.createPageInfo(Collections.emptyList());
        }
    }

    private Map<String, Object> buildEsSkuSortMap(List<String> fixSkus, List<String> recommentSkus) {
        int sortValue = 1000000;
        Map<String, Integer> esSkuSortMap = new HashMap<>();
        if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(fixSkus)) {
            for (int i = 0; i < fixSkus.size(); i++) {
                sortValue = sortValue - i;
                String sku = fixSkus.get(i);
                esSkuSortMap.put(sku, sortValue);
            }
        }

        if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(recommentSkus)) {
            for (int i = 0; i < recommentSkus.size(); i++) {
                sortValue = sortValue - i;
                String sku = recommentSkus.get(i);
                esSkuSortMap.put(sku, sortValue);
            }
        }
        Map<String, Object> map = new HashMap<>();
        map.put("sortSkus", esSkuSortMap);
        return map;
    }

    @Override
    public List<EsProductVO> querySuggestWordV3(HomeProductQueryVo homeProductQueryVo) {
        EsHomeProductQueryReq esHomeProductQueryReq = new EsHomeProductQueryReq();
        EsHomeProductQueryConditionReq conditionReq = new EsHomeProductQueryConditionReq();
        MerchantSubject merchantSubject = homeProductQueryVo.getMerchantSubject();
        Integer areaNo = RequestHolder.getMerchantAreaNo();
        esHomeProductQueryReq.setAreaNo(areaNo);
        esHomeProductQueryReq.setShow(homeProductQueryVo.getShow());
        //小程序过滤部分类目
        conditionReq.setMiniProgramLogin(RequestHolder.isMiniProgramLogin());
        //小程序过滤部分类目
        if (RequestHolder.isMiniProgramLogin()) {
            Set<Integer> unShowCategoryId = categoryService.getUnShowCategoryId();
            if (CollectionUtil.isNotEmpty(unShowCategoryId)) {
                esHomeProductQueryReq.setMiniProgramCategoryIds(unShowCategoryId);
            }
            //大客户过滤代仓商品
            if (RequestHolder.isMajor()) {
                List<Integer> unShowAdminId = Lists.newArrayList(450, 744, 1110, 1175, 1227, 1525);
                if (unShowAdminId.contains(RequestHolder.getAdminId())) {
                    Set<String> unShowSku = areaSkuService.selectAgentSku(RequestHolder.getAdminId(), RequestHolder.getMerchantAreaNo());
                    if (CollectionUtil.isNotEmpty(unShowSku)) {
                        esHomeProductQueryReq.setMiniProgramUnShowSkus(unShowSku);
                    }
                }
            }
        }
        List<String> skuList = homeProductQueryVo.getSkuList();
        if (merchantSubject.getSkuShow() == null && !CollectionUtils.isEmpty(skuList)) {
            //terms
            esHomeProductQueryReq.setSkuList(skuList);
        }
        //单店查询非大客户专享，大客户定量以报价单为准，全量查询非大客户专享+大客户专享
        if (merchantSubject.getSkuShow() == null && !CollectionUtils.isEmpty(skuList)) {
            //terms
            esHomeProductQueryReq.setSkuList(skuList);
        }
        conditionReq.setMajor(RequestHolder.isMajor());
        conditionReq.setSkuShow(merchantSubject.getSkuShow());
        if (RequestHolder.isMajor()) {
            if (!CollectionUtils.isEmpty(skuList)) {
                //报价单sku
                esHomeProductQueryReq.setMajorSkuList(homeProductQueryVo.getSkuList());
            }
            if (Objects.equals(merchantSubject.getSkuShow(), 2)) {
                //全量查询非大客户专享+有sku报价单的大客户专享
                esHomeProductQueryReq.setMType(0);
            }
        } else {
            //单店查询非大客户专享
            esHomeProductQueryReq.setMType(0);
        }

        if (StringUtils.isNotEmpty(homeProductQueryVo.getPdName())) {
            esHomeProductQueryReq.setPdName(homeProductQueryVo.getPdName());
        }
        esHomeProductQueryReq.setPageIndex(0);
        esHomeProductQueryReq.setPageSize(50);
        try {
            List<EsProductVO> esProductVOS = marketQueryProviderFacade.querySuggestWord(esHomeProductQueryReq, conditionReq);
            return esProductVOS;
        } catch (Exception e) {
            log.error("es商品搜索建议词获取异常", e);
            return new ArrayList<>();
        }
    }

    /**
     * Handles the sale price and major price processing for a list of product info VOs.
     * Updates the products with area SKU pricing and major pricing information.
     *
     * @param productInfoVOs List of product info VOs to process
     * @param areaSkuMap Map of area SKUs keyed by SKU ID
     * @param majorPriceMap Map of major prices keyed by SKU ID
     */
    private void handleSalePriceOrMajorPrice(List<ProductInfoVO> productInfoVOs, Map<String, AreaSku> areaSkuMap, Map<String, MajorPrice> majorPriceMap) {
        if (CollectionUtils.isEmpty(productInfoVOs)) {
            return;
        }
        Map<Integer, Category> categoryMap = categoryService.selectCategoryWithCachePipeline(productInfoVOs.stream().map(ProductInfoVO::getCategoryId).collect(Collectors.toSet()));
        productInfoVOs.forEach(productInfoVO -> processProductInfoVO(productInfoVO, areaSkuMap, majorPriceMap, categoryMap));
    }

    private void processProductInfoVO(ProductInfoVO productInfoVO, Map<String, AreaSku> areaSkuMap, Map<String, MajorPrice> majorPriceMap, Map<Integer, Category> categoryMap) {
        resetProductInfo(productInfoVO);
        setPriceInformation(productInfoVO, areaSkuMap);
        setMajorPriceInformation(productInfoVO, majorPriceMap);
        setCategory(productInfoVO, categoryMap);
    }

    private void resetProductInfo(ProductInfoVO productInfoVO) {
        //优先取SKU名称，如果为空，则兜底为SPU名称+SKU规格信息
        productInfoVO.resetSkuNameAndSkuPic();
    }

    private void setPriceInformation(ProductInfoVO productInfoVO, Map<String, AreaSku> areaSkuMap) {
        AreaSku areaSku = areaSkuMap.get(productInfoVO.getSku());
        if (areaSku != null) {
            productInfoVO.setLadderPrice(areaSku.getLadderPrice());
            handleLadderPrices(productInfoVO, areaSku);
            productInfoVO.setSalePrice(areaSku.getPrice().doubleValue());
            productInfoVO.setOriginalPrice(areaSku.getPrice().toString());
            productInfoVO.setActivityOriginPrice(areaSku.getPrice());
        }
    }

    private void handleLadderPrices(ProductInfoVO productInfoVO, AreaSku areaSku) {
        if (org.apache.commons.lang3.StringUtils.isNotBlank(areaSku.getLadderPrice()) || ObjectUtil.notEqual("[]", areaSku.getLadderPrice())) {
            List<LadderPriceVO> ladderPriceObjs = JSONObject.parseArray(areaSku.getLadderPrice(), LadderPriceVO.class);
            productInfoVO.setLadderPrices(ladderPriceObjs);
        }
    }

    private void setMajorPriceInformation(ProductInfoVO productInfoVO, Map<String, MajorPrice> majorPriceMap) {
        MajorPrice majorPrice = majorPriceMap.get(productInfoVO.getSku());
        if (Objects.nonNull(majorPrice)) {
            if(!Objects.equals (majorPrice.getPriceType (), MajorPriceTypeEnum.MALL_PRICE.getCode ())){
                BigDecimal price = PriceCalculator.calculateMajorPriceByType (majorPrice.getPrice(), BigDecimal.valueOf (productInfoVO.getSalePrice ()), majorPrice.getPriceAdjustmentValue (), majorPrice.getPriceType ());
                productInfoVO.setPrice(price.doubleValue ());
            }
            productInfoVO.setDirect(majorPrice.getDirect());
            productInfoVO.setMallShow(majorPrice.getMallShow());
        }
    }

    private void setCategory(ProductInfoVO productInfoVO, Map<Integer, Category> categoryMap) {
        Category category = categoryMap.get(productInfoVO.getCategoryId());
        if (category != null && null != category.getType()) {
            productInfoVO.setCateType(category.getType());
        }
    }

    private void fillAvgInfo(ProductInfoVO productInfoVO) {
        Set<Integer> xxsg = categoryService.getChildCategoryIdsByParentName ("新鲜水果");
        if(xxsg.contains (productInfoVO.getCategoryId ())) {
            AvgInfoVO avgInfoVO = inventoryService.getAvgInfo(productInfoVO.getCategoryId (),productInfoVO.getWeight ());
            productInfoVO.setAvgNumerator(avgInfoVO.getAvgNumerator ());
            productInfoVO.setAvgUnit(avgInfoVO.getAvgUnit ());
        }
        fillAvgWeight(productInfoVO);
    }
    private void fillAvgWeight(ProductInfoVO productInfoVO){
        if(ObjectUtil.isNotNull (productInfoVO.getWeightNum ()) && Constants.kgList.contains (productInfoVO.getNetWeightUnit ())){
            productInfoVO.setWeightNum (productInfoVO.getWeightNum ().multiply (new BigDecimal (2)));
            if(productInfoVO.getWeightNum ().compareTo (new BigDecimal (1))<0){
                productInfoVO.setWeightNum (null);
            }
        }
        if(ObjectUtil.isNotNull (productInfoVO.getNetWeightNum ()) && Constants.kgList.contains (productInfoVO.getNetWeightUnit ())){
            productInfoVO.setNetWeightNum (productInfoVO.getNetWeightNum ().multiply (new BigDecimal (2)));
            if(productInfoVO.getNetWeightNum ().compareTo (new BigDecimal (1))<0){
                productInfoVO.setNetWeightNum (null);
            }
        }
    }

    /**
     * 获取指定商户ID和SKU列表的区域库存信息。
     * 该方法查询与商户关联的指定SKU的库存信息。
     * 如果商户ID或SKU列表为空，则返回空Map。
     *
     * @param mId               商户ID，用于获取区域库存信息。
     * @param marketItemSkuList 需要查询库存信息的SKU列表。
     * @return 包含SKU库存信息的Map，键为SKU。如果商户ID或SKU列表为空，则返回空Map。
     */
    private Map<String, AreaStoreQueryRes> getAreaStoreMap(Long mId, List<String> marketItemSkuList) {
        if (mId == null || CollectionUtils.isEmpty(marketItemSkuList)) {
            log.error("mId:{}为空，或者marketItemSkuList为空，不可获取SKU库存:{}", mId, marketItemSkuList);
            return Collections.emptyMap();
        }
        // 查询库存
        Contact contact = contactService.getMerchantDefaultContactCache(mId);
        AreaStoreQueryReq queryReq = new AreaStoreQueryReq();
        queryReq.setContactId(contact != null ? contact.getContactId() : null);
        queryReq.setSkuCodeList(marketItemSkuList);
        queryReq.setMId(mId);
        queryReq.setSource(DistOrderSourceEnum.getDistOrderSource(RequestHolder.getBusinessLine()));
        return wmsAreaStoreFacade.getInfo(queryReq);
    }
}
