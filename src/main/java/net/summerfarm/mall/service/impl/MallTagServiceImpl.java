package net.summerfarm.mall.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.google.common.collect.Lists;

import java.util.*;

import lombok.extern.slf4j.Slf4j;
import net.summerfarm.mall.common.config.DynamicConfig;
import net.summerfarm.mall.common.util.RequestHolder;
import net.summerfarm.mall.enums.market.ItemTypeEnum;
import net.summerfarm.mall.enums.market.MallTagEnum;
import net.summerfarm.mall.enums.market.ScopeTypeEnum;
import net.summerfarm.mall.enums.market.TagTypeEnum;
import net.summerfarm.mall.mapper.InventoryMapper;
import net.summerfarm.mall.mapper.TagLaunchInfoMapper;
import net.summerfarm.mall.market.TagMarketHandler;
import net.summerfarm.mall.model.domain.Area;
import net.summerfarm.mall.model.domain.MerchantPoolDetail;
import net.summerfarm.mall.model.dto.market.malltag.MallTagDTO;
import net.summerfarm.mall.model.dto.market.malltag.MallTagReqDTO;
import net.summerfarm.mall.model.dto.market.malltag.SkuMallTagInfoDTO;
import net.summerfarm.mall.model.dto.market.malltag.TagScopeDTO;
import net.summerfarm.mall.model.dto.merchant.MerchantBasicDTO;
import net.summerfarm.mall.service.AreaService;
import net.summerfarm.mall.service.MallTagService;
import net.summerfarm.mall.service.MerchantPoolService;
import net.xianmu.common.result.CommonResult;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.Map.Entry;
import java.util.stream.Collectors;

/**
 * @author: <EMAIL>
 * @create: 2023/5/4
 */
@Slf4j
@Service
public class MallTagServiceImpl implements MallTagService {

    @Resource
    private TagLaunchInfoMapper tagLaunchInfoMapper;

    @Resource
    private AreaService areaService;

    @Resource
    private MerchantPoolService merchantPoolService;

    @Resource
    private InventoryMapper inventoryMapper;

    @Resource
    private ApplicationContext applicationContext;

    @Resource
    private DynamicConfig dynamicConfig;

    @Override
    public CommonResult<List<SkuMallTagInfoDTO>> listAllTags(MallTagReqDTO reqDTO) {
        List<SkuMallTagInfoDTO> skuMallTagInfoDTOArrayList = Lists.newArrayList();
        List<String> skus = Lists.newArrayList();
        if (reqDTO.getPdId() != null) {
            skus = inventoryMapper.selectByPdId(reqDTO.getPdId(),
                    RequestHolder.getMerchantAreaNo());
        }
        if (CollectionUtil.isNotEmpty(reqDTO.getSkus())) {
            skus = reqDTO.getSkus();
        }
        if (CollectionUtil.isEmpty(skus)) {
            return CommonResult.ok(skuMallTagInfoDTOArrayList);
        }
        skuMallTagInfoDTOArrayList = skus.stream().map(x -> {
            SkuMallTagInfoDTO tagInfoDTO = new SkuMallTagInfoDTO();
            tagInfoDTO.setSku(x);
            return tagInfoDTO;
        }).collect(Collectors.toList());

        Long mId = RequestHolder.getMId();
        Integer areaNo = RequestHolder.getMerchantAreaNo();
        boolean major = RequestHolder.isMajor();
        //结合营销信息分类标签
        List<MallTagDTO> tagDTOList = getTagLaunchInfos(skus, mId, areaNo);
        if (CollectionUtil.isNotEmpty(tagDTOList)) {
            //筛选出全部商品的
            List<MallTagDTO> allItemTags = tagDTOList.stream()
                    .filter(x -> Objects.equals(x.getItemType(), ItemTypeEnum.ALL.getCode()))
                    .collect(Collectors.toList());
            //匹配sku标签
            for (SkuMallTagInfoDTO tagInfoDTO : skuMallTagInfoDTOArrayList) {
                List<MallTagDTO> mallTags = Lists.newArrayList(allItemTags);
                List<MallTagDTO> skuMatchList = tagDTOList.stream()
                        .filter(x -> Objects.equals(tagInfoDTO.getSku(), x.getBizId())).collect(
                                Collectors.toList());
                if (CollectionUtil.isNotEmpty(skuMatchList)) {
                    mallTags.addAll(skuMatchList);
                }
                List<MallTagDTO> dtoList = mallTags.stream().map(x -> {
                    x.setBizType(MallTagEnum.getCodeByName(x.getName()));
                    return x;
                }).collect(Collectors.toList());

                MallTagDTO specialPriceTag = dtoList.stream().filter(
                                x -> Objects.equals(x.getBizType(), MallTagEnum.SPECIAL_OFFER.getCode()))
                        .findFirst().orElse(null);
                //后台不会配置省心送特价标签，只要有特价标签默认生效省心送特价标签
                if (specialPriceTag != null) {
                    MallTagDTO copy = specialPriceTag.copy();
                    copy.setBizType(MallTagEnum.TIMING_SPECIAL_OFFER.getCode());
                    dtoList.add(copy);
                }

                tagInfoDTO.setMallTags(dtoList);
                tagInfoDTO.setRemainTagEnums(Lists.newArrayList());
                tagInfoDTO.setBusinessTags(Lists.newArrayList());
                tagInfoDTO.setProfitTags(Lists.newArrayList());
                classifyTags(tagInfoDTO);
                //大客户不展示氛围标签
                if (major && CollectionUtil.isNotEmpty(tagInfoDTO.getAtmosphereTags())) { //NOSONAR
                    log.info("大客户展示特定氛围标签");
                    List<Long> supportAtmosphereTagIds = dynamicConfig.getMajorSupportAtmosphereTagIds();
                    tagInfoDTO.setAtmosphereTags(CollectionUtil.isEmpty(supportAtmosphereTagIds) ? Lists.newArrayList()
                            : tagInfoDTO.getAtmosphereTags().stream().filter(x -> supportAtmosphereTagIds.contains(x.getId())).collect(Collectors.toList()));
                }
            }
        }

        //处理营销信息
        Map<String, TagMarketHandler> handlerMap = applicationContext.getBeansOfType(
                TagMarketHandler.class);
        MerchantBasicDTO merchant = new MerchantBasicDTO();
        merchant.setAreaNo(areaNo);
        merchant.setIsMajor(major);
        merchant.setMId(mId);
        for (Entry<String, TagMarketHandler> handlerEntry : handlerMap.entrySet()) {
            TagMarketHandler handler = handlerEntry.getValue();
            handler.handler(skuMallTagInfoDTOArrayList, merchant);
        }

        //最终过滤
        for (SkuMallTagInfoDTO tagInfoDTO : skuMallTagInfoDTOArrayList) {
            /**
             * 如果没有剩余标签枚举，则清空业务标签、利益标签和商城标签，并跳过后续处理.
             */
            if(CollectionUtils.isEmpty(tagInfoDTO.getRemainTagEnums())){
                tagInfoDTO.setBusinessTags(Collections.emptyList());
                tagInfoDTO.setProfitTags(Collections.emptyList());
                tagInfoDTO.setMallTags(null);
                continue;
            }
            tagInfoDTO.getRemainTagEnums().sort(Comparator.comparing(MallTagEnum::getCode));
            Set<Integer> tagEnumCodeSet = tagInfoDTO.getRemainTagEnums().stream()
                    .sorted(Comparator.comparing(MallTagEnum::getCode))
                    .map(x -> x.getCode())
                    .collect(Collectors.toSet());
            /**
             * 过滤并排序最终的利益标签.
             * 1. 过滤掉业务类型不在剩余标签枚举集合中的利益标签.
             * 2. 按照权重降序、ID降序排序.
             */
            List<MallTagDTO> finalProfitTags = tagInfoDTO.getProfitTags().stream()
                    .filter(x -> tagEnumCodeSet.contains(x.getBizType()))
                    .sorted(Comparator.comparing(MallTagDTO::getWeight, Comparator.reverseOrder())
                            .thenComparing(MallTagDTO::getId, Comparator.reverseOrder())).collect(
                            Collectors.toList());
            tagInfoDTO.setProfitTags(finalProfitTags);

            /**
             * 过滤并排序最终的业务标签，并限制只保留一个.
             * 1. 过滤掉业务类型不在剩余标签枚举集合中的业务标签.
             * 2. 按照权重降序、ID降序排序.
             * 3. 限制结果集只取第一个.
             */
            List<MallTagDTO> finalBusinessTags = tagInfoDTO.getBusinessTags().stream()
                    .filter(x -> tagEnumCodeSet.contains(x.getBizType()))
                    .sorted(Comparator.comparing(MallTagDTO::getWeight, Comparator.reverseOrder())
                            .thenComparing(MallTagDTO::getId, Comparator.reverseOrder())).limit(1)
                    .collect(Collectors.toList());
            tagInfoDTO.setBusinessTags(finalBusinessTags);

            //清除
            tagInfoDTO.setMallTags(null);
        }

        return CommonResult.ok(skuMallTagInfoDTOArrayList);
    }

    /**
     * 获取所有标签
     *
     * @param skus
     * @param mId
     * @param areaNo
     * @return
     */
    private List<MallTagDTO> getTagLaunchInfos(List<String> skus, Long mId, Integer areaNo) {
        if (StringUtils.isEmpty(areaNo)){
            return null;
        }

        List<MerchantPoolDetail> poolDetails = merchantPoolService.getDetailByMIdWithCache(mId);
        Area area = areaService.selectAreaWithCache(areaNo);
        Integer largeAreaNo = area.getLargeAreaNo();
        List<TagScopeDTO> scopes = Lists.newArrayList();
        scopes.add(new TagScopeDTO(areaNo.longValue(), ScopeTypeEnum.AREA.getCode()));

        if (!CollectionUtils.isEmpty(poolDetails)) {
            poolDetails.forEach(x -> {
                scopes.add(new TagScopeDTO(x.getPoolInfoId(),
                        ScopeTypeEnum.MERCHANT_POOL.getCode()));
            });
        }
        scopes.add(
                new TagScopeDTO(largeAreaNo.longValue(), ScopeTypeEnum.LARGE_AREA.getCode()));

        List<MallTagDTO> tagDTOList = tagLaunchInfoMapper.listAll(scopes, skus);
        return tagDTOList;
    }

    private void classifyTags(SkuMallTagInfoDTO tagInfoDTO) {
        List<MallTagDTO> mallTags = tagInfoDTO.getMallTags();
        Map<Integer, List<MallTagDTO>> typeMap = mallTags.stream()
                .collect(Collectors.groupingBy(MallTagDTO::getType));

        //氛围标签处理
        List<MallTagDTO> atmosphereResult = typeMap.get(TagTypeEnum.ATMOSPHERE.getCode());
        if (CollectionUtil.isNotEmpty(atmosphereResult)) {
            List<MallTagDTO> atmosphereTags = atmosphereResult.stream()
                    .sorted(Comparator.comparing(MallTagDTO::getWeight, Comparator.reverseOrder())
                            .thenComparing(MallTagDTO::getId, Comparator.reverseOrder())).limit(1)
                    .collect(
                            Collectors.toList());
            tagInfoDTO.setAtmosphereTags(atmosphereTags);
        }

        //利益标签处理
        List<MallTagDTO> profitTags = typeMap.get(TagTypeEnum.PROFIT.getCode());
        tagInfoDTO.setProfitTags(
                CollectionUtil.isNotEmpty(profitTags) ? profitTags : Lists.newArrayList());

        //业务标签处理
        List<MallTagDTO> businessTags = typeMap.get(TagTypeEnum.BUSINESS.getCode());
        tagInfoDTO.setBusinessTags(
                CollectionUtil.isNotEmpty(businessTags) ? businessTags : Lists.newArrayList());


    }

}
