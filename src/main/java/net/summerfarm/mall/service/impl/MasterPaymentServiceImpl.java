package net.summerfarm.mall.service.impl;

import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.common.exceptions.DefaultServiceException;
import net.summerfarm.common.util.StringUtils;
import net.summerfarm.mall.common.util.DateUtils;
import net.summerfarm.mall.constant.CommonRedisKey;
import net.summerfarm.mall.enums.MarketRuleCouponRuleEnum;
import net.summerfarm.mall.enums.MarketRuleHistorySendStatusEnum;
import net.summerfarm.mall.enums.MarketRuleHistoryTypeEnum;
import net.summerfarm.mall.enums.OrderStatusEnum;
import net.summerfarm.mall.enums.payment.PaymentEnums;
import net.summerfarm.mall.mapper.MarketRuleHistoryMapper;
import net.summerfarm.mall.mapper.MasterPaymentMapper;
import net.summerfarm.mall.mapper.MerchantMapper;
import net.summerfarm.mall.model.domain.*;
import net.summerfarm.mall.service.MasterOrderService;
import net.summerfarm.mall.service.MasterPaymentService;
import net.summerfarm.mall.service.OrderRelationService;
import net.summerfarm.mall.service.PaymentService;
import net.summerfarm.mall.service.helper.OrderServiceHelper;
import net.xianmu.redis.support.lock.annotation.XmLock;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2023/10/11  17:11
 */
@Service
@Slf4j
public class MasterPaymentServiceImpl implements MasterPaymentService {
    @Resource
    private MasterPaymentMapper masterPaymentMapper;
    @Resource
    private PaymentService paymentService;
    @Resource
    private OrderRelationService orderRelationService;
    @Resource
    private MasterOrderService masterOrderService;
    @Resource
    private MarketRuleHistoryMapper marketRuleHistoryMapper;
    @Resource
    private MerchantMapper merchantMapper;
    @Resource
    private OrderServiceHelper orderServiceHelper;


    @Override
    public MasterPayment selectByMasterPaymentNo(String masterPaymentNo) {
        return masterPaymentMapper.selectByMasterPaymentNo(masterPaymentNo);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void upsertMasterAndPayment(MasterPayment masterPayment, List<Orders> ordersList) {
        //更新主单信息
        MasterPayment old = selectByMasterPaymentNo(masterPayment.getMasterOrderNo());
        if (old == null) {
            masterPaymentMapper.insertSelective(masterPayment);
        } else {
            if (!Objects.equals(PaymentEnums.PaymentStatus.PAYING.getStatus(), old.getStatus())) {
                throw new DefaultServiceException(0, "付款已完成，请勿重复支付");
            }

            old.setTransactionNumber(masterPayment.getTransactionNumber());
            old.setPayType(masterPayment.getPayType());
            old.setCompanyAccountId(masterPayment.getCompanyAccountId());
            old.setStatus(0);
            old.setScanCode(masterPayment.getScanCode());
            old.setBocPayType(masterPayment.getBocPayType());
            masterPaymentMapper.updateByPrimaryKeySelective(old);
        }

        //更新子单信息
        if (!CollectionUtils.isEmpty(ordersList)){
            for (Orders orders : ordersList) {
                paymentService.insertPayment(masterPayment.getTransactionNumber(), orders.getOrderNo(), masterPayment.getPayType(), masterPayment.getCompanyAccountId(), null, masterPayment.getBocPayType());
            }
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void masterPaymentNotifyHandler(MasterPayment masterPayment) {
        //更新主单状态
        masterOrderService.updateMasterOrderStatus(masterPayment.getMasterOrderNo(), OrderStatusEnum.NO_PAYMENT, OrderStatusEnum.DELIVERING);

        //更新主支付单信息
        masterPaymentMapper.updateByMasterPaymentNoSelective(masterPayment);

        //更新子支付单信息
        List<Orders> ordersList = orderRelationService.selectOrdersByMasterOrderNo(masterPayment.getMasterOrderNo());
        for (Orders orders : ordersList) {
            Payment payment = new Payment();
            payment.setPayType(masterPayment.getPayType());
            payment.setOrderNo(orders.getOrderNo());
            payment.setTransactionNumber(masterPayment.getTransactionNumber());
            payment.setMoney(orders.getTotalPrice());
            payment.setEndTime(DateUtils.localDateTime2Date(masterPayment.getEndTime()));
            payment.setTradeType(masterPayment.getTradeType());
            payment.setBankType(masterPayment.getBankType());
            payment.setBocPayType(masterPayment.getBocPayType());
            payment.setStatus(masterPayment.getStatus());
            payment.setCompanyAccountId(masterPayment.getCompanyAccountId());
            payment.setOnlinePayEndTime(DateUtils.localDateTime2Date(masterPayment.getOnlinePayEndTime()));

            paymentService.notifySuccess(payment, orders);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateStatusByMasterPaymentNo(String masterOrderNo, Integer status) {
        //更新主支付单信息
        MasterPayment updateMasterPayment = new MasterPayment();
        updateMasterPayment.setMasterOrderNo(masterOrderNo);
        updateMasterPayment.setStatus(status);
        masterPaymentMapper.updateByMasterPaymentNoSelective(updateMasterPayment);

        //更新子支付单信息
        List<Orders> ordersList = orderRelationService.selectOrdersByMasterOrderNo(masterOrderNo);
        for (Orders orders : ordersList) {
            paymentService.updateStatusByPaymentNo(orders.getOrderNo(), status);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateMoneyByMasterPaymentNo(String masterOrderNo, BigDecimal totalPrice) {
        //更新主支付单信息
        MasterPayment updateMasterPayment = new MasterPayment();
        updateMasterPayment.setMasterOrderNo(masterOrderNo);
        updateMasterPayment.setMoney(totalPrice);
        masterPaymentMapper.updateByMasterPaymentNoSelective(updateMasterPayment);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @XmLock(prefixKey = CommonRedisKey.Lock.PAY_NOTIFY_FULL_RETURN, key = "{masterOrder.masterOrderNo}", expireTime = 30 * 60L, waitTime = 3000L)
    public void sendFullReturn(MasterOrder masterOrder) {
        log.info("支付成功回调，发送满返券masterOrder:{}", JSON.toJSONString(masterOrder));
        if (masterOrder == null || masterOrder.getMasterOrderNo() == null) {
            log.warn("MasterPaymentService[]sendFullReturn[]masterOrder is null!");
            return;
        }

        //1、根据主订单号查询满返记录
        String masterOrderNo = masterOrder.getMasterOrderNo();
        List<MarketRuleHistory> marketRuleHistoryList = marketRuleHistoryMapper.select(masterOrderNo, MarketRuleHistoryTypeEnum.FULL_RETURN.getCode());
        if (CollectionUtils.isEmpty(marketRuleHistoryList)) {
            log.info("MasterPaymentService[]sendFullReturn[]marketRuleHistoryList is empty!masterOrder:{}", JSON.toJSONString(masterOrder));
            return;
        }

        //2、判断是否支付后发放满返券
        Long mId = masterOrder.getMId();
        if (mId == null) {
            MasterOrder byMasterOrderNo = masterOrderService.findByMasterOrderNo(masterOrderNo);
            mId = byMasterOrderNo.getMId();
        }
        Merchant merchant = merchantMapper.selectOneByMid(mId);
        for (MarketRuleHistory marketRuleHistory : marketRuleHistoryList) {
            String detail = marketRuleHistory.getDetail();
            if (StringUtils.isBlank(detail)) {
                log.warn("执行支付成功后发放满返券操作失败，满返规则为空！marketRuleHistory:{}", JSON.toJSONString(marketRuleHistory));
                continue;
            }

            //校验状态防止重复发放
            if (!Objects.equals(marketRuleHistory.getSendStatus(), MarketRuleHistorySendStatusEnum.TO_BE_ISSUED.getCode())) {
                log.warn("当前满返发放状态不为待发放状态！marketRuleHistory:{}", JSON.toJSONString(marketRuleHistory));
                continue;
            }

            //判断是否支付成功后才返券
            MarketRule marketRule = JSON.parseObject(marketRuleHistory.getDetail(), MarketRule.class);
            if (Objects.equals(marketRule.getCouponRule(), MarketRuleCouponRuleEnum.PAYMENT_COMPLETION.getCode())) {
                //发券
                orderServiceHelper.sendCoupon(marketRuleHistory, masterOrderNo, merchant, marketRule);
            }
        }
    }
}
