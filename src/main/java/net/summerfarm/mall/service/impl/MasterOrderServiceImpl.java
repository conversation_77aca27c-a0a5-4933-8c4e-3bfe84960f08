package net.summerfarm.mall.service.impl;

import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.common.util.StringUtils;
import net.summerfarm.enums.OrderTypeEnum;
import net.summerfarm.enums.SaleStockChangeTypeEnum;
import net.summerfarm.mall.client.provider.OrderProvider;
import net.summerfarm.mall.client.req.OrderReq;
import net.summerfarm.mall.common.delayqueue.OrderCancelItem;
import net.summerfarm.mall.common.util.DateUtils;
import net.summerfarm.mall.common.util.RequestHolder;
import net.summerfarm.mall.contexts.Global;
import net.summerfarm.mall.contexts.OrderPayStatusConstant;
import net.summerfarm.mall.enums.DistOrderSourceEnum;
import net.summerfarm.mall.enums.MajorDirectEnum;
import net.summerfarm.mall.enums.OrderStatusEnum;
import net.summerfarm.mall.enums.payment.PaymentEnums;
import net.summerfarm.mall.mapper.MasterOrderMapper;
import net.summerfarm.mall.mapper.MerchantMapper;
import net.summerfarm.mall.mapper.OrderItemMapper;
import net.summerfarm.mall.mapper.OrdersMapper;
import net.summerfarm.mall.model.domain.*;
import net.summerfarm.mall.model.vo.DeliveryPlanVO;
import net.summerfarm.mall.payments.request.PaymentHandler;
import net.summerfarm.mall.service.*;
import net.summerfarm.mall.service.facade.WmsAreaStoreFacade;
import net.summerfarm.mall.service.facade.WncDeliveryFenceQueryFacade;
import net.summerfarm.mall.service.facade.dto.AreaStoreUnLockReq;
import net.summerfarm.mall.service.facade.dto.FenceCloseTimeReq;
import net.summerfarm.mall.service.facade.dto.OrderUnLockSkuDetailReqDTO;
import net.summerfarm.mall.service.popmall.PopOrderRelationService;
import net.summerfarm.mall.wechat.templatemessage.TemplateMsgSender;
import net.summerfarm.mall.wechat.templatemessage.TimeoutCloseMsg;
import net.summerfarm.warehouse.service.WarehouseLogisticsService;
import net.xianmu.common.exception.BizException;
import net.xianmu.common.result.CommonResult;
import net.xianmu.common.result.ResultStatusEnum;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/10/11  14:23
 */
@Slf4j
@Service
public class MasterOrderServiceImpl implements MasterOrderService {
    @Resource
    private MasterOrderMapper masterOrderMapper;
    @Resource
    private OrderRelationService orderRelationService;
    @Resource
    private OrdersMapper ordersMapper;
    @Resource
    private WarehouseLogisticsService logisticsService;
    @Resource
    private DeliveryPlanService deliveryPlanService;
    @Resource
    private PaymentHandler paymentHandler;
    @Resource
    private RedisTemplate redisTemplate;
    @Resource
    private OrderItemMapper orderItemMapper;
    @Resource
    private MasterPaymentService masterPaymentService;
    @Resource
    private MerchantCouponService merchantCouponService;
    @Resource
    private PrepayInventoryService prepayInventoryService;
    @Resource
    private MerchantCardService merchantCardService;
    @Resource
    private WmsAreaStoreFacade wmsAreaStoreFacade;
    @Resource
    private MerchantService merchantService;
    @Resource
    private TemplateMsgSender templateMsgSender;
    @Resource
    private MerchantMapper merchantMapper;
    @Resource
    private ActivityService activityService;
    @Resource
    private PopOrderRelationService popOrderRelationService;
    @Resource
    private OrderProvider orderProvider;


    @Override
    public MasterOrder findByMasterOrderNo(String masterOrderNo) {
        return masterOrderMapper.selectByMasterOrderNo(masterOrderNo);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateAccountIdByMasterOrderNo(String masterOrderNo, Long accountId) {
        List<Orders> ordersList = orderRelationService.selectOrdersByMasterOrderNo(masterOrderNo);
        for (Orders orders : ordersList) {
            Orders record = new Orders();
            record.setOrderNo(orders.getOrderNo());
            record.setAccountId(RequestHolder.getAccountId());
            ordersMapper.updateByOrderNoSelective(record);
        }

        MasterOrder masterOrderUpdate = new MasterOrder();
        masterOrderUpdate.setMasterOrderNo(masterOrderNo);
        masterOrderUpdate.setAccountId(accountId);
        masterOrderMapper.updateByMasterOrderSelective(masterOrderUpdate);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateMasterOrderAndSubOrderStatus(String masterOrderNo, OrderStatusEnum oldStatus, OrderStatusEnum newStatus) {
        int rc = masterOrderMapper.updateMasterOrderStatus(masterOrderNo, oldStatus == null ? null : oldStatus.getId(), newStatus.getId());
        if (rc == 0) {
            return false;
        }
        log.info("更新主订单状态，主订单编号：{}，旧状态：{}，新状态：{}", masterOrderNo, oldStatus == null ? null : oldStatus.getId(), newStatus.getId());

        List<Orders> ordersList = orderRelationService.selectOrdersByMasterOrderNo(masterOrderNo);
        for (Orders orders : ordersList) {
            Orders record = new Orders();
            record.setOrderNo(orders.getOrderNo());
            record.setStatus(Integer.valueOf(newStatus.getId()).shortValue());
            ordersMapper.updateByOrderNoSelective(record);

            orderItemMapper.updateStatusByOrderNo(orders.getOrderNo(), newStatus.getId());
        }

        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateMasterOrderStatus(String masterOrderNo, OrderStatusEnum oldStatus, OrderStatusEnum newStatus) {
        int rc = masterOrderMapper.updateMasterOrderStatus(masterOrderNo, oldStatus == null ? null : oldStatus.getId(), newStatus.getId());
        if (rc == 0) {
            return false;
        }
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public CommonResult<Boolean> closeOrder(String masterOrderNo, Long mId, Boolean isMajor, Boolean isMajorDirect) {
        if (StringUtils.isBlank(masterOrderNo)) {
            return CommonResult.fail(ResultStatusEnum.BAD_REQUEST, "参数异常，订单编号不可为空");
        }
        MasterOrder masterOrder = masterOrderMapper.selectByMasterOrderNo(masterOrderNo);
        if (masterOrder == null || !Objects.equals(masterOrder.getMId(), mId)) {
            return CommonResult.fail(ResultStatusEnum.BAD_REQUEST, "参数异常，订单不存在");
        }

        List<Orders> ordersList = orderRelationService.selectOrdersByMasterOrderNo(masterOrderNo);
        if (CollectionUtils.isEmpty(ordersList)) {
            return CommonResult.fail(ResultStatusEnum.BAD_REQUEST, "参数异常，订单不存在");
        }
        Integer orderType = ordersList.get(0).getType();

        //大客户账期关单不需要调用三方支付关单，检验订单状态和截单时间
        if (isMajorDirect) {
            if (masterOrder.getStatus() != OrderStatusEnum.NO_PAYMENT.getId()) {
                return CommonResult.fail(ResultStatusEnum.BAD_REQUEST, "订单不能被取消，请联系相关人员");
            }

            //校验截单时间：已过截单时间的订单不能被取消
            String orderNo = ordersList.get(0).getOrderNo();
            List<DeliveryPlanVO> voList = deliveryPlanService.selectByOrderNo(orderNo);
            if (CollectionUtils.isEmpty(voList)) {
                return CommonResult.fail(ResultStatusEnum.BAD_REQUEST, "订单不能被取消，请联系相关人员");
            }
//            LocalTime localTime = logisticsService.selectCloseTime(voList.get(0).getOrderStoreNo());
//            LocalDateTime closeTime = LocalDateTime.of(LocalDate.now(), localTime);
//            if (LocalDateTime.now().plusMinutes(5).isAfter(closeTime)) {
//                return CommonResult.fail(ResultStatusEnum.BAD_REQUEST, "订单不能被取消，请联系相关人员");
//            }

            //其他客户可以待支付状态取消
        } else if (masterOrder.getStatus() != OrderStatusEnum.NO_PAYMENT.getId()
                && ordersList.stream().anyMatch(el -> el.getStatus() != OrderStatusEnum.NO_PAYMENT.getId())) {
            return CommonResult.fail(ResultStatusEnum.BAD_REQUEST, "订单已支付或已关闭，不可取消");
        }

        //关闭支付单：先反查订单是否支付，已支付使用redis锁防止错误关闭
        try {
            paymentHandler.closeOrderV2(masterOrderNo);
        } catch (Exception e) {
            // 允许关单失败，记录异常即可
            log.error("关闭支付单{}失败", masterOrderNo, e);
        }
        String key = OrderPayStatusConstant.ORDER_PAY_SUCCESS_STATUS + masterOrderNo;
        if (redisTemplate.hasKey(key)) {
            log.error("关闭订单{}失败，订单支付成功", masterOrderNo);
            redisTemplate.delete(key);
            return CommonResult.fail(ResultStatusEnum.BAD_REQUEST, "取消订单失败，订单已支付成功");
        }

        //更新订单状态
        boolean updateResult = updateMasterOrderAndSubOrderStatus(masterOrderNo, OrderStatusEnum.NO_PAYMENT, OrderStatusEnum.CANCEL);
        if (!updateResult) {
            return CommonResult.fail(ResultStatusEnum.BAD_REQUEST, "订单状态异常，不可取消");
        }

        //更新支付单状态
        masterPaymentService.updateStatusByMasterPaymentNo(masterOrderNo, PaymentEnums.PaymentStatus.CANCEL.getStatus());
        log.info("关闭订单{}，更新支付状态", masterOrderNo);

        //更新配送计划状态
        List<String> orderNoList = ordersList.stream().map(Orders::getOrderNo).collect(Collectors.toList());
        deliveryPlanService.updateDeliveryPlanStatus(orderNoList, OrderStatusEnum.CANCEL.getId());
        log.info("关闭订单{}，更新配送计划状态", masterOrderNo);

        //处理营销逻辑：释放优惠券、返还黄金卡使用次数、返还大客户预付次数
        merchantCouponService.releaseMerchantCouponByOrder(masterOrderNo, masterOrder.getMId());
        merchantCardService.cancelOrder(orderNoList);
        if (isMajor) {
            orderNoList.forEach(orderNo -> prepayInventoryService.increaseByOrderNo(orderNo));
        }
        log.info("关闭订单{}，处理营销数据", masterOrderNo);

        Merchant merchant = merchantMapper.selectOneByMid(mId);
        //处理库存：释放冻结库存
        List<DeliveryPlan> deliveryPlanList = deliveryPlanService.selectByOrderNoBatch(orderNoList);
        deliveryPlanList.stream().sorted(Comparator.comparing(DeliveryPlan::getDeliveryTime)).forEach(el -> {
            List<OrderItem> orderItems = orderItemMapper.selectOrderItem(el.getOrderNo());

            AreaStoreUnLockReq unLockReq = new AreaStoreUnLockReq();
            unLockReq.setContactId(el.getContactId());
            unLockReq.setOrderType(SaleStockChangeTypeEnum.CANCEL_ORDER.getTypeName());
            unLockReq.setOrderNo(el.getOrderNo());
            unLockReq.setIdempotentNo(el.getOrderNo());
            unLockReq.setOperatorNo(el.getOrderNo());
            unLockReq.setOperatorName(merchant.getMname());
            unLockReq.setSource(DistOrderSourceEnum.getDistOrderSourceByOrderType(orderType));
            List<OrderUnLockSkuDetailReqDTO> dtos = new ArrayList<>();
            for (OrderItem orderItem : orderItems) {
                OrderUnLockSkuDetailReqDTO orderUnLockSkuDetailReqDTO = new OrderUnLockSkuDetailReqDTO();
                orderUnLockSkuDetailReqDTO.setSkuCode(orderItem.getSku());
                orderUnLockSkuDetailReqDTO.setReleaseQuantity(orderItem.getAmount());
                dtos.add(orderUnLockSkuDetailReqDTO);
                //反还活动库存
                activityService.orderCancelActivityQuantity(el.getOrderNo(),orderItem.getSku(),merchant.getmId());
            }
            unLockReq.setOrderReleaseSkuDetailReqDTOS(dtos);
            wmsAreaStoreFacade.storeUnLock(unLockReq);
        });

        log.info("关闭订单{}，释放冻结库存", masterOrderNo);

        log.info("关闭订单{}，同时关闭pop订单对应的代下单", masterOrderNo);
        // 处理pop订单
        if (OrderTypeEnum.POP.getId().equals(orderType)) {
            for (String popOrderNo : orderNoList) {
                String helpOrderNo = popOrderRelationService.findHelpOrderByPopOrderNo(popOrderNo);
                if (StringUtils.isEmpty(helpOrderNo)){
                    continue;
                }
                log.info("开始关闭pop订单{}对应的代下单{}", popOrderNo, helpOrderNo);
                try {
                    OrderReq orderReq = new OrderReq();
                    orderReq.setOrderNo(helpOrderNo);
                    orderProvider.closeOrder(orderReq);
                } catch (Exception e) {
                    throw new BizException("pop订单对应的代下单订单关单失败，原因：" + e.getMessage());
                }
                log.info("结束关闭pop订单{}对应的代下单{}", popOrderNo, helpOrderNo);
            }
        }

        return CommonResult.ok(true);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void timeOutCloseV2(OrderCancelItem orderCancelItem) {
        String orderNo = orderCancelItem.getOrderNo();
        log.info("订单{}开始超时关单...", orderNo);
        Merchant merchant = merchantService.queryMerchantInfo(orderCancelItem.getmId());

        //调用关单接口
        CommonResult<Boolean> res = closeOrder(orderCancelItem.getOrderNo(),
                orderCancelItem.getmId(),
                Global.BIG_MERCHANT.equals(merchant.getSize()),
                MajorDirectEnum.PERIOD.getType().equals(merchant.getDirect()));
        log.info("订单{}超时关单结果：{}", orderNo, JSON.toJSONString(res));

        //关单成功发送微信消息
        if (res.getData() != null && res.getData()) {
            List<Orders> ordersList = orderRelationService.selectOrdersByMasterOrderNo(orderNo);
            String orderTime = DateUtils.date2String(new Date(orderCancelItem.getRunTime() - 30 * 60 * 1000), DateUtils.LONG_DATE_FORMAT);
            String openId = StringUtils.isBlank(orderCancelItem.getOpenid()) ? merchant.getOpenid() : orderCancelItem.getOpenid();
            String msg = TimeoutCloseMsg.templateMessage(ordersList.get(0).getOrderNo(), orderTime, openId);
            log.info("订单" + orderNo + "微信超时关闭订单发送消息结果：" + msg);
            try {
                templateMsgSender.sendTemplateMsg(msg);
            } catch (Exception e) {
                log.error("订单" + orderNo + "微信超时关闭订单发送消息失败", e);
            }
        }
    }

    @Override
    public boolean isPopOrder(String masterOrderNo) {
        if (StringUtils.isBlank(masterOrderNo)) {
            return false;
        }
        MasterOrder masterOrder = masterOrderMapper.selectByMasterOrderNo(masterOrderNo);
        if (masterOrder == null) {
            return false;
        }
        return Objects.equals(masterOrder.getType(), OrderTypeEnum.POP.getId());
    }
}
