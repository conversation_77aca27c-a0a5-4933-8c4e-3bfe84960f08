package net.summerfarm.mall.service;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 支付状态同步服务
 * <AUTHOR>
 * @date 2024-12-20
 */
public interface PaymentSyncService {
    
    /**
     * 同步支付中状态的支付单
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param limit 限制条数
     */
    void syncPayingPayments(LocalDateTime startTime, LocalDateTime endTime, Integer limit);
    
    /**
     * 根据支付单号列表同步支付状态
     * @param orderNos 支付单号列表
     */
    void syncPaymentsByOrderNos(List<String> orderNos);
    
    /**
     * 根据主支付单号列表同步支付状态
     * @param masterOrderNos 主支付单号列表
     */
    void syncMasterPaymentsByOrderNos(List<String> masterOrderNos);
}
