package net.summerfarm.mall.service;


import net.summerfarm.mall.model.domain.MarketPriceControlProducts;

import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0
 * @project summerfarm-mall
 * @description 控价品
 * @date 2024/06/07 11:49:56
 */
public interface MarketPriceControlProductsService {

    /**
     * @description: 查询所有控价品信息--默认走缓存 缓存时间 1h
     * @author: lzh
     * @date: 2023/10/10 11:55
     * @param:
     * @return:
     **/
    Map<String, MarketPriceControlProducts> selectAllControlProductsByCache();
}
