//package net.summerfarm.mall.task.payment;
//
//import com.alibaba.fastjson.JSON;
//import com.alibaba.schedulerx.worker.processor.ProcessResult;
//import lombok.extern.slf4j.Slf4j;
//import net.summerfarm.mall.payments.common.config.PaymentConfig;
//import net.summerfarm.mall.service.PaymentSyncService;
//import net.xianmu.task.process.XianMuJavaProcessorV2;
//import net.xianmu.task.vo.input.XmJobInput;
//import org.springframework.stereotype.Component;
//import org.springframework.util.StringUtils;
//
//import javax.annotation.Resource;
//import java.time.LocalDateTime;
//import java.time.format.DateTimeFormatter;
//import java.util.Arrays;
//import java.util.List;
//import java.util.Map;
//
///**
// * 支付状态同步定时任务
// * <AUTHOR>
// * @date 2025-05-26
// */
//@Component
//@Slf4j
//public class PaymentSyncJob extends XianMuJavaProcessorV2 {
//
//    @Resource
//    private PaymentSyncService paymentSyncService;
//
//    @Resource
//    private PaymentConfig paymentConfig;
//
//    private static final DateTimeFormatter FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
//
//    @Override
//    public ProcessResult processResult(XmJobInput context) throws Exception {
//        log.info("支付状态同步定时任务开始执行，参数：{}", context.getJobParameters());
//
//        try {
//            String jobParameters = context.getJobParameters();
//
//            if (StringUtils.hasText(jobParameters)) {
//                // 解析任务参数
//                Map<String, Object> params = JSON.parseObject(jobParameters, Map.class);
//
//                // 检查是否指定了支付单号
//                if (params.containsKey("orderNos")) {
//                    String orderNosStr = (String) params.get("orderNos");
//                    List<String> orderNos = Arrays.asList(orderNosStr.split(","));
//                    paymentSyncService.syncPaymentsByOrderNos(orderNos);
//                    log.info("根据支付单号同步完成");
//                    return new ProcessResult(true);
//                }
//
//                // 检查是否指定了主支付单号
//                if (params.containsKey("masterOrderNos")) {
//                    String masterOrderNosStr = (String) params.get("masterOrderNos");
//                    List<String> masterOrderNos = Arrays.asList(masterOrderNosStr.split(","));
//                    paymentSyncService.syncMasterPaymentsByOrderNos(masterOrderNos);
//                    log.info("根据主支付单号同步完成");
//                    return new ProcessResult(true);
//                }
//
//                // 解析时间范围参数
//                LocalDateTime startTime = null;
//                LocalDateTime endTime = null;
//                Integer limit = paymentConfig.getSyncDefaultLimit();
//
//                if (params.containsKey("startTime")) {
//                    String startTimeStr = (String) params.get("startTime");
//                    startTime = LocalDateTime.parse(startTimeStr, FORMATTER);
//                }
//
//                if (params.containsKey("endTime")) {
//                    String endTimeStr = (String) params.get("endTime");
//                    endTime = LocalDateTime.parse(endTimeStr, FORMATTER);
//                }
//
//                if (params.containsKey("limit")) {
//                    limit = Integer.valueOf(params.get("limit").toString());
//                }
//
//                // 如果没有指定时间范围，使用配置的默认时间范围
//                if (startTime == null && endTime == null) {
//                    endTime = LocalDateTime.now();
//                    startTime = endTime.minusMinutes(paymentConfig.getSyncDefaultMinutes());
//                }
//
//                paymentSyncService.syncPayingPayments(startTime, endTime, limit);
//            } else {
//                // 没有参数时，使用配置的默认参数同步支付中状态的支付单
//                LocalDateTime endTime = LocalDateTime.now();
//                LocalDateTime startTime = endTime.minusMinutes(paymentConfig.getSyncDefaultMinutes());
//                paymentSyncService.syncPayingPayments(startTime, endTime, paymentConfig.getSyncDefaultLimit());
//            }
//
//            log.info("支付状态同步定时任务执行完成");
//            return new ProcessResult(true);
//        } catch (Exception e) {
//            log.error("支付状态同步定时任务执行失败", e);
//            return new ProcessResult(false, e.getMessage());
//        }
//    }
//}
