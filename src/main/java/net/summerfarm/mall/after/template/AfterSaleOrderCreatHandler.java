package net.summerfarm.mall.after.template;

import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.common.AjaxResult;
import net.summerfarm.common.exceptions.DefaultServiceException;
import net.summerfarm.enums.AfterSaleHandleType;
import net.summerfarm.enums.AfterSaleOrderStatus;
import net.summerfarm.enums.OrderTypeEnum;
import net.summerfarm.mall.after.template.impl.AuditAfterSolution;
import net.summerfarm.mall.after.template.impl.HandleAfterSolution;
import net.summerfarm.mall.contexts.OrderPayStatusConstant;
import net.summerfarm.mall.enums.AfterRefundTypeEnum;
import net.summerfarm.mall.enums.CommonStatus;
import net.summerfarm.mall.enums.InventoryTypeEnum;
import net.summerfarm.mall.contexts.AfterSaleOrderAction;
import net.summerfarm.mall.contexts.Global;
import net.summerfarm.mall.enums.AfterSaleDeliveryedEnum;
import net.summerfarm.mall.factory.AfterSaleSolutionOrderFactory;
import net.summerfarm.mall.mapper.*;
import net.summerfarm.mall.model.domain.*;
import net.summerfarm.mall.model.vo.AfterSaleOrderVO;
import net.summerfarm.mall.model.vo.DeliveryPlanVO;
import net.summerfarm.mall.service.AfterSaleOrderService;
import net.summerfarm.mall.service.DeliverPlanRemarkSnapshotService;
import net.summerfarm.mall.service.MemberService;
import net.summerfarm.mall.service.helper.AfterSaleOrderHelper;
import net.xianmu.common.exception.BizException;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;
import org.springframework.util.CollectionUtils;
import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @date 2022.06.30
 * 生成售后单模板抽象类
 */
@Service
@Slf4j
public class AfterSaleOrderCreatHandler {
    @Resource
    @Lazy
    private AfterSaleOrderHelper afterSaleOrderHelper;
    @Resource
    private AfterSaleOrderMapper afterSaleOrderMapper;
    @Resource
    private AfterSaleProofMapper afterSaleProofMapper;
    @Resource
    private MemberService memberService;
    @Resource
    private MerchantMapper merchantMapper;
    @Resource
    protected InventoryMapper inventoryMapper;
    @Resource
    private AfterSaleOrderService afterSaleOrderService;
    @Resource
    private PrepayInventoryRecordMapper prepayInventoryRecordMapper;
    @Resource
    private AfterSaleOrderAction afterSaleOrderAction;
    @Resource
    private AfterSaleSolutionOrderFactory afterSaleSolutionOrderFactory;
    @Resource
    DeliverPlanRemarkSnapshotService deliverPlanRemarkSnapshotService;
    @Resource
    private RedissonClient redissonClient;
    @Resource
    private DeliveryPlanMapper deliveryPlanMapper;
    /**
     * 生成售后单
     * @return
     */
    @Transactional(propagation = Propagation.REQUIRED,rollbackFor=Exception.class)
    public AjaxResult creatAfterSaleOrder(BaseCreatAfterSaleSolution solution,AfterSaleOrderVO afterSaleOrderVO, Orders orders){
        //赠品逻辑
        if (afterSaleOrderVO.getSuitId() != null && afterSaleOrderVO.getSuitId() == -1 ){
            return AjaxResult.getErrorWithMsg("抱歉，赠品暂不提供售后服务");
        }
        //拦截录入账单逻辑
        if (afterSaleOrderVO.getHandleType().equals(AfterSaleHandleType.BLOCK_INCOMING_BILLS.getType())){
            return AjaxResult.getErrorWithMsg("抱歉，拦截录入的账单暂不提供售后服务");
        }
        //如果有回收费，将回收费减去
        if (afterSaleOrderVO.getRecoveryNum() != null && afterSaleOrderVO.getRecoveryNum().compareTo(BigDecimal.ZERO) >0){
            BigDecimal realMoney = afterSaleOrderVO.getHandleNum().subtract(afterSaleOrderVO.getRecoveryNum());
            if (realMoney.compareTo(BigDecimal.ZERO) <=0){
                return AjaxResult.getErrorWithMsg("退款金额需大于回收费");
            }
            afterSaleOrderVO.setHandleNum(realMoney);
        }

        //拦截订单不可发起售后 查询配送计划的拦截状态判断是否被拦截
        if (Objects.equals(orders.getType(), OrderTypeEnum.TIMING.getId())) {
            DeliveryPlan deliveryPlan = deliveryPlanMapper.selectById(afterSaleOrderVO.getDeliveryId());
            if (Objects.nonNull(deliveryPlan) && Objects.equals(deliveryPlan.getInterceptFlag(), CommonStatus.YES.getCode())) {
                return AjaxResult.getErrorWithMsg("当前订单正在处理拦截退款，暂不受理其他售后");
            }
        } else {
            List<DeliveryPlanVO> deliveryPlanVOS = deliveryPlanMapper.selectByOrderNo(orders.getOrderNo());
            if (!CollectionUtils.isEmpty(deliveryPlanVOS) && Objects.equals(deliveryPlanVOS.get(0).getInterceptFlag(), CommonStatus.YES.getCode())) {
                return AjaxResult.getErrorWithMsg("当前订单正在处理拦截退款，暂不受理其他售后");
            }
        }

        Inventory inventory = inventoryMapper.selectBySku(afterSaleOrderVO.getSku());
        Integer skuType = null;
        if (inventory!=null){
            skuType = inventory.getType();
        }
        //生成售后单号
        String afterSaleOrderNo = afterSaleOrderHelper.createAfterSaleOrderNo();
        afterSaleOrderVO.setAfterSaleOrderNo(afterSaleOrderNo);
        //钩子判定
        if (hasBasicCheck(afterSaleOrderVO)){
            //基本校验
            AjaxResult basicCheck = basicCheck(afterSaleOrderVO, orders,skuType);
            log.info("售后基本校验"+basicCheck);
            if(!AjaxResult.isSuccess(basicCheck)){
                return basicCheck;
            }
        }
        //极速售后判定
        if(Objects.equals(afterSaleOrderVO.getType(),1) ){
            AjaxResult fastAfter = fastAfterSale(afterSaleOrderVO, orders);
            if (!AjaxResult.isSuccess(fastAfter)){
                return fastAfter;
            }
        }

        //自定义校验
        AjaxResult customizeCheck = solution.customizeCheck(afterSaleOrderVO, orders);
        log.info("售后自定义校验"+customizeCheck);
        if(!AjaxResult.isSuccess(customizeCheck)){
            return customizeCheck;
        }
        //校验是否为商城发起的售后,并填充数据
        afterSaleOrderHelper.assembleAfterSaleOrderVO(afterSaleOrderVO);

        // 切仓库存不足售后归还虚拟库存
        if (Objects.equals(afterSaleOrderVO.getRefundType(), AfterRefundTypeEnum.SWITCH_WAREHOUSE.getDescription()) &&
                afterSaleOrderVO.getDeliveryed().equals(AfterSaleDeliveryedEnum.NOT_NEED.getType())){
            afterSaleOrderAction.releaseLockQuantity(afterSaleOrderVO);
        }

        //售后单生成
        assembleAfterSale(afterSaleOrderVO, orders);
        //工作流进行
        AjaxResult workflow = workflow(afterSaleOrderVO, orders);
        if(!AjaxResult.isSuccess(workflow)){
            //手动回滚
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            return workflow;
        }

        return AjaxResult.getOK();
    }

    /**
     *
     * @param afterSaleOrderVO
     * @param orders
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public AjaxResult basicCheck(AfterSaleOrderVO afterSaleOrderVO,Orders orders,Integer skuType){
        //校验基本售后单数据
        AjaxResult checkAfterSaleOrder = afterSaleOrderHelper.checkAfterSaleOrder(orders, afterSaleOrderVO);
        if(!AjaxResult.isSuccess(checkAfterSaleOrder)){
            return checkAfterSaleOrder;
        }
        Integer quantity = afterSaleOrderVO.getQuantity();
        //数量校验
        AjaxResult maxQuantity = afterSaleOrderService.getMaxQuantity(afterSaleOrderVO);
        log.info("最大售后数量："+maxQuantity);
        if(!AjaxResult.isSuccess(maxQuantity)){
            return maxQuantity;
        }

        //未到货售后校验数量
        if (Objects.equals(afterSaleOrderVO.getDeliveryed(),AfterSaleOrder.DELIVERY_NOT_RECEIVED)){
            if (quantity == null || quantity <= 0 || !quantity.equals((Integer) maxQuantity.getData())){
                return AjaxResult.getErrorWithMsg("未到货售后需申请全部数量");
            }
        }

        //订单拦截不做校验
        if (!Arrays.asList(new Integer[]{11, 12}).contains(afterSaleOrderVO.getHandleType())){
            if (quantity == null || quantity <= 0 || quantity > (Integer)maxQuantity.getData()){
                return AjaxResult.getErrorWithMsg("售后数量需在1~" + maxQuantity.getData() + "件之间");
            }
        }
        BigDecimal handleNum = afterSaleOrderVO.getHandleNum();
        Merchant merchant = merchantMapper.selectOneByMid(orders.getmId());
        List<PrepayInventoryRecord> prepayInventoryRecords = prepayInventoryRecordMapper.selectRecordList(merchant.getAdminId(), afterSaleOrderVO.getOrderNo(), afterSaleOrderVO.getSku());

        //换货补发不需要做校验
        if (!Arrays.asList(new Integer[]{6, 7}).contains(afterSaleOrderVO.getHandleType())){
            //代仓商品的实付是0元,预付款商品实付0元 ,退款支持0元
            if (CollectionUtils.isEmpty(prepayInventoryRecords) && handleNum.compareTo(BigDecimal.ZERO) == 0 && !Objects.equals(skuType, InventoryTypeEnum.AGENT_PRODUCT.getType())  ) {
                return AjaxResult.getErrorWithMsg("退款金额异常");
            }
        }
        //通过最大数量计算最大金额，否则会有问题
        afterSaleOrderVO.setQuantity((Integer) maxQuantity.getData());
        AjaxResult afterSaleMoney;
        if(null != afterSaleOrderVO.getIsManage() && !afterSaleOrderVO.getIsManage()){
             afterSaleMoney = afterSaleOrderService.getAfterSaleMoney(afterSaleOrderVO);
        }else {
             afterSaleMoney = afterSaleOrderService.getAfterSaleMoneyForAfterSale(afterSaleOrderVO);
        }
        if (!AjaxResult.isSuccess(afterSaleMoney)){
            log.info("计算最大金额失败 >>> {}", JSON.toJSONString(afterSaleMoney));
            return afterSaleMoney;
        }
        //将原来的quantity设置回去
        afterSaleOrderVO.setQuantity(quantity);
        log.info("最大金额"+afterSaleMoney);
        if (afterSaleOrderVO.getHandleNum().compareTo((BigDecimal)afterSaleMoney.getData()) > 0 || handleNum.compareTo(BigDecimal.ZERO) < 0){
            return AjaxResult.getErrorWithMsg("请输入有效退款金额（不超过" + afterSaleMoney.getData() + "元）");
        }


        return AjaxResult.getOK();
    }


    /**
     * 拼装并生成售后单
     * @return
     */
    public AjaxResult assembleAfterSale(AfterSaleOrderVO afterSaleOrderVO,Orders orders){

        //生成售后单号并设置
        afterSaleOrderVO.setStatus(AfterSaleOrderStatus.WAIT_HANDLE.getStatus());
        AfterSaleOrder afterSaleOrder = new AfterSaleOrder();
        AfterSaleProof afterSaleProof = new AfterSaleProof();
        Integer grade = memberService.calculGrade(afterSaleOrderVO.getmId());
        afterSaleOrder.setOrderNo(afterSaleOrderVO.getOrderNo());
        afterSaleOrder.setStatus(afterSaleOrderVO.getStatus());
        afterSaleOrder.setDeliveryed(afterSaleOrderVO.getDeliveryed());
        afterSaleOrder.setmId(afterSaleOrderVO.getmId());
        afterSaleOrder.setAfterSaleOrderStatus(afterSaleOrderVO.getAfterSaleOrderStatus());
        afterSaleOrder.setDeliveryId(afterSaleOrderVO.getDeliveryId());
        afterSaleOrder.setAccountId(afterSaleOrderVO.getAccountId());
        afterSaleOrder.setAddTime(LocalDateTime.now());
        afterSaleOrder.setOrderNo(afterSaleOrderVO.getOrderNo());
        afterSaleOrder.setSku(afterSaleOrderVO.getSku());
        afterSaleOrder.setAfterSaleUnit(afterSaleOrderVO.getAfterSaleUnit());
        afterSaleOrder.setIsFull((afterSaleOrderVO.getType() != null && afterSaleOrderVO.getType() == 3) ? Boolean.TRUE : Boolean.FALSE);
        afterSaleOrder.setSuitId(afterSaleOrderVO.getSuitId());
        afterSaleOrder.setGrade(grade);
        afterSaleOrder.setAfterSaleOrderNo(afterSaleOrderVO.getAfterSaleOrderNo());
        afterSaleOrder.setRecoveryType(afterSaleOrderVO.getRecoveryType());
        afterSaleOrder.setType(afterSaleOrderVO.getType());
        afterSaleOrder.setProductType(afterSaleOrderVO.getProductType());
        afterSaleOrder.setCarryingGoods(afterSaleOrderVO.getCarryingGoods());
        //计算售后了几次
        int times = 1;
        AfterSaleOrder select = new AfterSaleOrder();
        select.setOrderNo(afterSaleOrderVO.getOrderNo());
        select.setSku(afterSaleOrderVO.getSku());
        select.setSuitId(afterSaleOrderVO.getSuitId());
        List<AfterSaleOrderVO> afterSaleOrders = afterSaleOrderService.selectAfterSaleOrderVO(select);
        if (!CollectionUtils.isEmpty(afterSaleOrders)) {
            times = afterSaleOrders.size() + 1;
        }
        afterSaleOrder.setTimes(times);
        afterSaleOrder.setView(1);
        afterSaleOrder.setAfterSaleRemark(afterSaleOrderVO.getAfterSaleRemark());
        afterSaleOrder.setAfterSaleRemarkType(afterSaleOrderVO.getAfterSaleRemarkType());
        afterSaleOrder.setRefundFreight(afterSaleOrderVO.getRefundFreight());
        afterSaleOrder.setSnapshot(afterSaleOrderVO.getSnapshot());
        afterSaleOrder.setAutoAfterSaleFlag(afterSaleOrderVO.getAutoAfterSaleFlag());
        afterSaleOrderMapper.insertSelective(afterSaleOrder);

        afterSaleProof.setHandleNum(afterSaleOrderVO.getHandleNum().setScale(2));
        afterSaleProof.setAfterSaleOrderNo(afterSaleOrderVO.getAfterSaleOrderNo());
        afterSaleProof.setHandleType(afterSaleOrderVO.getHandleType());
        afterSaleProof.setStatus(afterSaleOrderVO.getStatus());
        afterSaleProof.setAfterSaleType(afterSaleOrderVO.getAfterSaleType());
        afterSaleProof.setQuantity(afterSaleOrderVO.getQuantity());
        afterSaleProof.setRefundType(afterSaleOrderVO.getRefundType());
        afterSaleProof.setProofPic(afterSaleOrderVO.getProofPic());
        afterSaleProof.setApplyRemark(afterSaleOrderVO.getApplyRemark());
        afterSaleProof.setApplyer(afterSaleOrderVO.getApplyer());
        afterSaleProof.setRecoveryNum(afterSaleOrderVO.getRecoveryNum());
        afterSaleProof.setAfterSaleOrderNo(afterSaleOrderVO.getAfterSaleOrderNo());
        afterSaleProof.setHandleRemark(afterSaleOrderVO.getHandleRemark());
        afterSaleProof.setExtraRemark(afterSaleOrderVO.getExtraRemark());
        afterSaleProof.setHandleSecondaryRemark(afterSaleOrderVO.getHandleSecondaryRemark());
        afterSaleProof.setApplySecondaryRemark(afterSaleOrderVO.getApplySecondaryRemark());
        afterSaleProof.setProofVideo(afterSaleOrderVO.getProofVideo());
        afterSaleProofMapper.insert(afterSaleProof);
        if(orders.getType().equals(OrderTypeEnum.TIMING.getId())){
            //省心送有这个id
            deliverPlanRemarkSnapshotService.addAfterSnapshot(afterSaleOrderVO.getDeliveryId(), afterSaleOrderVO.getAfterSaleOrderNo());
        }else {
            deliverPlanRemarkSnapshotService.addAfterSnapshot(afterSaleOrder.getOrderNo(),afterSaleOrder.getAfterSaleOrderNo());
        }
        return AjaxResult.getOK();
    }

    /**
     * 钩子方法，钩住基本校验
     * @return
     */
    public boolean hasBasicCheck(AfterSaleOrderVO afterSaleOrderVO){
        if (afterSaleOrderVO.getHandleType().equals(AfterSaleHandleType.BLOCK_INCOMING_BILLS.getType())
                || afterSaleOrderVO.getHandleType().equals(AfterSaleHandleType.BLOCK_REFUNDS.getType())
                || afterSaleOrderVO.getHandleType().equals(AfterSaleHandleType.RETURN_SHIPPING.getType())
                || afterSaleOrderVO.getHandleType().equals(AfterSaleHandleType.RETURN_SHIPPING_BILL.getType())) {
            return false;
        }
        return true;
    }

    //极速售后
    public AjaxResult fastAfterSale(AfterSaleOrderVO afterSaleOrderVO,Orders orders){
        //极速售后 校验客户类型 大客户无法发起
        Integer grade = memberService.calculGrade(afterSaleOrderVO.getmId());
        Integer afterCount = afterSaleOrderMapper.selectHBAfterByOrderNo(afterSaleOrderVO.getOrderNo());
        if (afterCount>0){
            return AjaxResult.getError("包含补发/换货的极速售后不可发起");
        }
        if(!Objects.equals(orders.getmSize(), Global.BIG_MERCHANT)){
            if (afterSaleOrderVO.getHandleNum().compareTo(BigDecimal.valueOf(0)) == -1) {
                return AjaxResult.getErrorWithMsg("非水果类目不能极速售后");
            } else if (afterSaleOrderVO.getHandleNum().compareTo(BigDecimal.valueOf(0)) == 0) {
                return AjaxResult.getErrorWithMsg("极速售后金额必须大于0");
            } /*else if (afterSaleOrderVO.getHandleNum().compareTo(BigDecimal.valueOf(40)) == 1) {
                return AjaxResult.getErrorWithMsg("超出单次极速售后金额上限(40)");
            }*/
            BigDecimal refundAmount = memberService.getRefundAmountByGrade(grade);
            //当月已使用极速退款金额 计算剩余额度
            BigDecimal jsAmount = memberService.getUsedAmount(LocalDateTime.now(), afterSaleOrderVO.getmId(), grade);
            //剩余额度
            BigDecimal remainAmount = refundAmount.subtract(jsAmount == null ? BigDecimal.valueOf(0) : jsAmount);
            if(remainAmount.compareTo(afterSaleOrderVO.getHandleNum()) < 0){
                return AjaxResult.getErrorWithMsg("极速售后余额不足");
            }
        }else {
            return AjaxResult.getErrorWithMsg("大客户不可发起极速售后");
        }
        //极速售后只有返券
        afterSaleOrderVO.setHandleType(AfterSaleHandleType.COUPON.getType());
        return AjaxResult.getOK();
    }

    /**
     * 工作流处理，售后单状态至待审批或待审核
     * @param afterSaleOrderVO
     * @param orders
     * @return
     */
    public AjaxResult workflow(AfterSaleOrderVO afterSaleOrderVO,Orders orders){
        Boolean isManage = afterSaleOrderVO.getIsManage();
        Integer handleType = afterSaleOrderVO.getHandleType();
        Integer deliveryed = afterSaleOrderVO.getDeliveryed();
        //后台发起
        if (isManage){
            //已到货返券,已到货退款/录入账单,已到货拒收退款/拒收录入账单，换货补发，未到货退款、录入账单，需要走自动审核
            boolean handle = ((deliveryed.equals(AfterSaleDeliveryedEnum.NOT_NEED.getType()) && Arrays.asList(new Integer[]{2, 3}).contains(handleType))
                    || (deliveryed.equals(AfterSaleDeliveryedEnum.BROKEN.getType()) && Arrays.asList(new Integer[]{0, 2, 3, 9, 6,7,10}).contains(handleType)));
            if (handle){
                //审核
                HandleAfterSolution handleSolution = afterSaleSolutionOrderFactory.getHandleSolution(afterSaleOrderVO, orders);
                AfterSaleOrderHandleHandler afterSaleOrderHandleHandler = handleSolution.getAfterSaleOrderHandleHandler();
                AjaxResult result = afterSaleOrderHandleHandler.handleAfterSaleOrder(handleSolution,afterSaleOrderVO, orders);
                if (!AjaxResult.isSuccess(result)){
                    return result;
                }
            }
        }

        if(isManage){
            //未到货退款(省心送订单不走),已到货返券,换货，补发 需要走自动审批
            boolean audit = ((deliveryed.equals(AfterSaleDeliveryedEnum.BROKEN.getType()) && Arrays.asList(new Integer[]{0 ,6,7}).contains(handleType) )
                    || (deliveryed.equals(AfterSaleDeliveryedEnum.NOT_NEED.getType()) && Arrays.asList(new Integer[]{2, 3}).contains(handleType)&& !orders.getType().equals(OrderTypeEnum.TIMING.getId())));
            if (audit){
                //审批
                //上古逻辑，status为1审核通过，0 审核不通过
                afterSaleOrderVO.setStatus(1);
                afterSaleOrderVO.setAuditer(afterSaleOrderVO.getApplyer());
                AuditAfterSolution auditAfterSolution = afterSaleSolutionOrderFactory.getAuditSolution(afterSaleOrderVO, orders);
                AfterSaleOrderAuditHandler afterSaleOrderAuditHandler = auditAfterSolution.getAfterSaleOrderAuditHandler();
                AjaxResult result = afterSaleOrderAuditHandler.auditAfterSaleOrder(auditAfterSolution, afterSaleOrderVO, orders);
                if (!AjaxResult.isSuccess(result)){
                    return result;
                }
            }
        }

        //极速售后，商城未到货售后，且原因不是其他，自动审批审核通过
        if (!isManage){
            RLock redissonLock = redissonClient.getLock(OrderPayStatusConstant.AFTER_SALE_AUTO_WORKFLOW + orders.getmId());
            try {
                boolean flag = redissonLock.tryLock(30L, 30, TimeUnit.SECONDS);
                if (!flag) {
                    throw new BizException(1, "请重新提交售后");
                }
                if(Objects.equals(afterSaleOrderVO.getType(),1) || (afterSaleOrderVO.getDeliveryed().equals(AfterSaleDeliveryedEnum.NOT_NEED.getType()) && !afterSaleOrderVO.getRefundType().equals(AfterRefundTypeEnum.OTHER.getDescription()))){
                    //审核
                    HandleAfterSolution handleSolution = afterSaleSolutionOrderFactory.getHandleSolution(afterSaleOrderVO, orders);
                    AfterSaleOrderHandleHandler afterSaleOrderHandleHandler = handleSolution.getAfterSaleOrderHandleHandler();
                    AjaxResult result = afterSaleOrderHandleHandler.handleAfterSaleOrder(handleSolution,afterSaleOrderVO, orders);
                    if (!AjaxResult.isSuccess(result)){
                        return result;
                    }
                    //审批
                    //上古逻辑，status为1审核通过，0 审核不通过
                    afterSaleOrderVO.setStatus(1);
                    afterSaleOrderVO.setAuditer(afterSaleOrderVO.getApplyer());
                    AuditAfterSolution auditAfterSolution = afterSaleSolutionOrderFactory.getAuditSolution(afterSaleOrderVO, orders);
                    AfterSaleOrderAuditHandler afterSaleOrderAuditHandler = auditAfterSolution.getAfterSaleOrderAuditHandler();
                    AjaxResult result1 = afterSaleOrderAuditHandler.auditAfterSaleOrder(auditAfterSolution, afterSaleOrderVO, orders);
                    if (!AjaxResult.isSuccess(result1)){
                        return result1;
                    }
                }
            } catch (InterruptedException e) {
                log.error("锁获取异常", e);
                throw new DefaultServiceException(1, "请重新提交售后");
            } finally {
                if (redissonLock.isLocked() && redissonLock.isHeldByCurrentThread()){
                    redissonLock.unlock();
                }
            }
        }
        return AjaxResult.getOK();
    }
}
