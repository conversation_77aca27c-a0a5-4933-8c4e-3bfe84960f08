package net.summerfarm.mall.payments.common.utils;

import java.time.LocalDateTime;

/**
 * @description:
 * @author: <PERSON>
 * @date: 2025-05-28
 **/
public class PaymentNoGenerator {

    public static final String PAYMENT_NO_PREFIX = "PAY";

    public static String generatePaymentNo() {
        // PAY + 年份 + 随机字符串
        return PAYMENT_NO_PREFIX + LocalDateTime.now().getYear() + RandomStringGenerator.generateRandomString(16);
    }
}
