package net.summerfarm.mall.payments.common.config;

import com.alibaba.nacos.api.config.annotation.NacosValue;
import lombok.Data;
import net.summerfarm.mall.payments.common.enums.CmbTransferWechatDirectPayEnum;
import org.springframework.context.annotation.Configuration;

import java.util.Set;

/**
 * @description:
 * @author: George
 * @date: 2024-07-01
 **/
@Configuration
@Data
public class PaymentConfig {

    /**
     * pop 微信账户id
     */
    @NacosValue(value = "${payment.pop.wechat.company.account.id:108}", autoRefreshed = true)
    private Integer popWechatCompanyAccountId;

    /** 招行转微信直连标识（临时）
     * {@link CmbTransferWechatDirectPayEnum}
     */
    @NacosValue(value = "${payment.cmb.transfer.wechat.direct.pay:0}", autoRefreshed = true)
    private Integer cmbTransferWechatDirectPay;

    /**
     * 招行转微信直连区域黑名单
     */
    @NacosValue(value = "${payment.cmb.transfer.wechat.direct.pay.black.areas:}", autoRefreshed = true)
    private Set<Integer> cmbTransferWechatDirectPayBlackAreas;

    /**
     * 微信直连路由信息
     */
    @NacosValue(value = "${payment.wechat.pay.routing.info:}", autoRefreshed = true)
    private String wechatPayRoutingInfo;

    /**
     * 路由灰度配置（区域 + mid维度）
     */
    @NacosValue(value = "${payment.route.gray.scale.info:}", autoRefreshed = true)
    private String routeGrayScale;

    /**
     * B2B路由灰度配置（mid维度）
     */
    @NacosValue(value = "${payment.b2b.gray.scale.mid:}", autoRefreshed = true)
    private Set<Long> b2bRouteGrayScaleMIds;

    /**
     * B2B路由灰度配置（areaNo维度）
     */
    @NacosValue(value = "${payment.b2b.gray.scale.areas:}", autoRefreshed = true)
    private Set<Integer> b2bRouteGrayScaleAreas;

    /**
     * B2B路由灰度配置（开关）
     */
    @NacosValue(value = "${payment.b2b.gray.scale.switch:}", autoRefreshed = true)
    private String b2bRouteGrayScaleSwitch;

    /**
     * 延迟取消支付订单时间，单位：毫秒，默认20分钟
     */
    @NacosValue(value = "${payment.b2b.delay.cancel.pay.order.time:1200000}", autoRefreshed = true)
    private Long fireFaceDelayCancelPayOrderTime;

    @NacosValue(value = "${payment.temp.routing.config:}", autoRefreshed = true)
    private String tempRoutingConfig;

    /**
     * 退款静默时间(分钟)
     */
    @NacosValue(value = "${payment.refund.quite.time.in.minutes:10}", autoRefreshed = true)
    private Integer refundQuiteTimeInMinutes;

    /**
     * 默认支付方式配置
     */
    @NacosValue(value = "${payment.default.payment.method.config:}", autoRefreshed = true)
    private String defaultPaymentMethodConfigByArea;

    /**
     * 校验支付是否繁忙时间(秒)
     */
    @NacosValue(value = "${payment.check.pay.busy.seconds:30}", autoRefreshed = true)
    private Integer checkPayBusySeconds;

    /**
     * B2B路由开关状态(1开启，0关闭)
     * @see net.summerfarm.payment.routing.common.enums.PaymentB2bSwitchEnums
     */
    @NacosValue(value = "${payment.b2b.switch:1}", autoRefreshed = true)
    private Integer b2bSwitch;

    /**
     * 支付状态同步默认时间范围（分钟）
     */
    @NacosValue(value = "${payment.sync.default.minutes:1}", autoRefreshed = true)
    private Integer syncDefaultMinutes;

    /**
     * 支付状态同步默认限制条数
     */
    @NacosValue(value = "${payment.sync.default.limit:50}", autoRefreshed = true)
    private Integer syncDefaultLimit;

    /**
     * 支付状态同步最大限制条数
     */
    @NacosValue(value = "${payment.sync.max.limit:500}", autoRefreshed = true)
    private Integer syncMaxLimit;

}
