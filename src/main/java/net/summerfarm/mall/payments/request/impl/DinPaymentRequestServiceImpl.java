package net.summerfarm.mall.payments.request.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.common.AjaxResult;
import net.summerfarm.enums.HandleEventStatus;
import net.summerfarm.enums.RefundReponseEnum;
import net.summerfarm.mall.Conf;
import net.summerfarm.mall.common.util.DinUtils;
import net.summerfarm.mall.common.util.IPUtil;
import net.summerfarm.mall.common.util.RequestHolder;
import net.summerfarm.mall.contexts.Global;
import net.summerfarm.mall.contexts.SpringContextUtil;
import net.summerfarm.mall.mapper.RefundHandleEventMapper;
import net.summerfarm.mall.model.domain.MasterPayment;
import net.summerfarm.mall.model.vo.payment.PayResultVO;
import net.summerfarm.mall.payments.common.enums.*;
import net.summerfarm.mall.payments.common.pojo.*;
import net.summerfarm.mall.payments.common.pojo.din.*;
import net.summerfarm.mall.payments.common.pojo.din.notify.DinPayNotifyDTO;
import net.summerfarm.mall.payments.common.pojo.din.request.*;
import net.summerfarm.mall.payments.common.pojo.din.response.*;
import net.summerfarm.mall.payments.common.utils.CertUtils;
import net.summerfarm.mall.payments.common.utils.SM2Utils;
import net.summerfarm.mall.payments.common.utils.SM4Utils;
import net.summerfarm.mall.payments.notify.DinNotifyService;
import net.summerfarm.mall.payments.request.PaymentRequestService;
import net.summerfarm.mall.service.MasterPaymentService;
import net.summerfarm.mall.service.MerchantSubAccountService;
import net.summerfarm.mall.service.PaymentService;
import net.xianmu.common.exception.ProviderException;
import org.bouncycastle.jce.provider.BouncyCastleProvider;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.security.*;
import java.util.Collections;
import java.util.List;
import java.util.function.Function;

import static net.summerfarm.enums.RefundReponseEnum.P;

/**
 * @description:
 * @author: George
 * @date: 2025-01-07
 **/
@Slf4j
@Service("dinPaymentRequestService")
public class DinPaymentRequestServiceImpl implements PaymentRequestService {

    @Resource
    private MasterPaymentService masterPaymentService;
    @Resource
    private PaymentService paymentService;
    @Resource
    private DinNotifyService dinNotifyService;
    @Resource
    private RefundHandleEventMapper refundHandleEventMapper;
    @Resource
    private MerchantSubAccountService merchantSubAccountService;

    private static final String SIGNATURE_METHOD = "SM3WITHSM2";

    /**
     * 支付通用请求
     * 用于封装公共逻辑类似加密、签名、请求、验签等
     * @param payOrderInfo
     * @param requestBuilder
     * @param responseClass
     * @return
     * @param <T>
     * @param <R>
     */
    private <T extends BasePayOrderInfo, S, R> DinResponseDTO<R> dinCommonRequest(String url, T payOrderInfo, Function<T, S> requestBuilder, Class<R> responseClass) {
        Security.addProvider(new BouncyCastleProvider());

        DinPayConfig dinPayConfig = payOrderInfo.getDinPayConfig();
        PrivateKey merchantPrivateKey = CertUtils.getPrivateKeyByBase64(dinPayConfig.getPrivateKey());
        PublicKey platformPublicKey = CertUtils.getPublicKeyByBase64(dinPayConfig.getPublicKey());

        String encryptionKey = dinPayConfig.getSecret();
        String encryptedEncryptionKey = SM2Utils.encryptToBase64(platformPublicKey, encryptionKey);

        S requestDTO = requestBuilder.apply(payOrderInfo);
        String requestJson = JSON.toJSONString(requestDTO);
        String encryptedData = SM4Utils.encryptBase64(requestJson, encryptionKey);
        String signature = SM2Utils.sign(merchantPrivateKey, encryptedData);

        DinRequestDTO dinRequestDTO = buildDinRequestParams(dinPayConfig.getMerchantNo(), encryptedEncryptionKey, signature, encryptedData);

        String requestBody = JSON.toJSONString(dinRequestDTO);
        log.info("智付请求参数:{}", requestJson);
        url = DinPaymentEnum.domain.determineDomain(SpringContextUtil.isProduct()).concat(url);
        DinResponseDTO<R> response = DinUtils.executeRequest(url, requestBody, responseClass, platformPublicKey);
        log.info("智付响应参数:{}", JSON.toJSONString(response));
        return response;
    }


    private DinRequestDTO buildDinRequestParams(String merchantNo, String encryptedEncryptionKey, String signature, String encryptedData) {
        return new DinRequestDTO(merchantNo, encryptedEncryptionKey, SIGNATURE_METHOD, signature, String.format("%014d", System.currentTimeMillis()), encryptedData);
    }

    /**
     * 支付请求
     * @param payOrderInfo
     * @return
     * @param <T>
     */
    public <T extends BasePayOrderInfo> DinResponseDTO<DinPayResponseDTO> commonPayRequest(T payOrderInfo) {
        // 做一些公共校验
        commonPayRequestCheck(payOrderInfo);
        // 获取支付url
        String url = determinePayUrl(payOrderInfo.getIsMpPay());
        // 发起支付请求
        DinResponseDTO<DinPayResponseDTO> response = dinCommonRequest(url, payOrderInfo, this::buildPaymentRequestData, DinPayResponseDTO.class);
        // 校验支付请求结果
        checkDinResponse(response, Collections.singletonList(DinPaymentEnum.responseCode.SUCCESS.getCode()));
        return response;
    }

    private <T extends BasePayOrderInfo> void commonPayRequestCheck(T payOrderInfo) {
        // 智付公众号不支持0.01元支付
        if (payOrderInfo.getPayTotalPrice().compareTo(new BigDecimal("0.01")) == 0 && !Boolean.TRUE.equals(payOrderInfo.getIsMpPay())) {
            throw new ProviderException("暂不支持0.01元支付");
        }
    }


    /**
     * 组装支付参数
     * @param payOrderInfo
     * @return
     * @param <T>
     */
    private <T extends BasePayOrderInfo> DinPayRequestDTO buildPaymentRequestData(T payOrderInfo) {
        String interfaceName = determineInterfaceName();
        String paymentType = DinPaymentEnum.paymentType.WECHAT.getType();
        String paymentMethods = determinePaymentMethods();
        String appId = determineAppId();
        String openId = determineOpenId();
        // 描述字段 会原路返回 这里记录当时的账号id
        String orderDesc = JSON.toJSONString(PaymentAttachInfoDTO.builder().companyAccountId(payOrderInfo.getCompanyAccountId()).build());
        return DinPayRequestDTO.builder()
                .interfaceName(interfaceName)
                .orderNo(payOrderInfo.getPayOrderNo())
                .paymentType(paymentType)
                .paymentMethods(paymentMethods)
                .appid(appId)
                .openId(openId)
                .payAmount(payOrderInfo.getPayTotalPrice())
                .currency("CNY")
                .orderIp(IPUtil.getIpAddress(RequestHolder.getRequest()))
                .goodsName("鲜沐农场直销水果")
                .notifyUrl(Global.DOMAIN_NAME + NotifyUrl.DIN_NOTIFY_URL)
                // 交易过期时间 29.5分钟
                .timeExpire("1770")
                .orderDesc(orderDesc)
                .build();
    }

    /**
     * 确定openId
     * @return
     */
    private String determineOpenId() {
        return merchantSubAccountService.getOpenId();
    }

    /**
     * 确定appId
     * @return
     */
    private String determineAppId() {
        return RequestHolder.isMiniProgramLogin() ? Conf.MP_APP_ID : Conf.APP_Id;
    }

    /**
     * 确定支付方式
     * @return
     */
    private String determinePaymentMethods() {
        return RequestHolder.isMiniProgramLogin() ? DinPaymentEnum.paymentMethods.MINI_APP.getMethod() : DinPaymentEnum.paymentMethods.PUBLIC.getMethod();
    }

    /**
     * 确定接口名称
     * @return
     */
    private String determineInterfaceName() {
        return RequestHolder.isMiniProgramLogin() ? DinPaymentEnum.interfaceName.MINI_APP.getName() : DinPaymentEnum.interfaceName.PUBLIC_ACCOUNT.getName();
    }

    /**
     * 小程序or公众号支付url
     * @param isMpPay
     * @return
     */
    private String determinePayUrl(boolean isMpPay) {
        return DinPaymentEnum.url.determinePayUrl(isMpPay);
    }

    @Override
    public String payRequest(PaymentOrderInfo payOrderInfo) {
        DinResponseDTO<DinPayResponseDTO> response = commonPayRequest(payOrderInfo);
        //插入支付信息
        upsertPayment(payOrderInfo);
        // 返回支付结果
        return buildPayResultStr(response.getData());
    }

    /**
     * 插入支付信息
     * @param payOrderInfo
     */
    private void upsertPayment(PaymentOrderInfo payOrderInfo) {
        paymentService.insertPayment(null, payOrderInfo.getPayOrderNo(), payOrderInfo.getPayTypeStr(), payOrderInfo.getCompanyAccountId(), null, null);
    }

    /**
     * 构建支付结果字符串
     * @param responseData
     * @return
     */
    private String buildPayResultStr(DinPayResponseDTO responseData) {
        return JSONObject.toJSONString(buildPayResultVO(responseData));
    }

    @Override
    public PayResultVO payRequestV2(PaymentOrderInfoV2 paymentOrderInfoV2) {
        // 发起支付请求
        DinResponseDTO<DinPayResponseDTO> responseData = commonPayRequest(paymentOrderInfoV2);
        //插入支付信息
        upsertMasterPayment(paymentOrderInfoV2);
        // 返回支付结果
        return buildPayResultVO(responseData.getData());
    }

    /**
     * 构建支付结果
     * @param data
     * @return
     */
    private <T extends BasePayOrderInfo> PayResultVO buildPayResultVO(DinPayResponseDTO data) {
        String payInfo = data.getPayInfo();
        JSONObject jsonObject = JSONObject.parseObject(payInfo);
        PayResultVO payResultVO = new PayResultVO();
        payResultVO.setReturncode("SUCCESS");
        payResultVO.setAppId(data.getAppid());
        payResultVO.setTimeStamp(jsonObject.getString("timeStamp"));
        payResultVO.setNonceStr(jsonObject.getString("nonceStr"));
        payResultVO.setPackageStr(jsonObject.getString("package"));
        payResultVO.setPaySign(jsonObject.getString("paySign"));
        payResultVO.setSignType(jsonObject.getString("signType"));
        return payResultVO;
    }

    /**
     * 插入主订单和支付信息
     * @param paymentOrderInfoV2
     */
    private void upsertMasterPayment(PaymentOrderInfoV2 paymentOrderInfoV2) {
        MasterPayment masterPayment = new MasterPayment();
        masterPayment.setMasterOrderNo(paymentOrderInfoV2.getOrderNo());
        masterPayment.setPayType(paymentOrderInfoV2.getPayTypeStr());
        masterPayment.setCompanyAccountId(paymentOrderInfoV2.getCompanyAccountId());
        masterPaymentService.upsertMasterAndPayment(masterPayment, paymentOrderInfoV2.getOrdersList());
    }

    /**
     * 同步支付结果
     * @param queryOrderInfo 支付查询
     */
    @Override
    public void syncPayResult(QueryOrderInfo queryOrderInfo) {
        DinResponseDTO<DinPayQueryResponseDTO> response = commonSyncPayResult(queryOrderInfo);
        commonOnSuccess(response);
    }

    /**
     * 同步支付结果V2
     * @param orderInfoV2 支付查询
     */
    @Override
    public void syncPayResultV2(QueryOrderInfoV2 orderInfoV2) {
        DinResponseDTO<DinPayQueryResponseDTO> response = commonSyncPayResult(orderInfoV2);
        commonOnSuccess(response);
    }

    /**
     * 支付成功操作
     * @param response
     */
    private void commonOnSuccess(DinResponseDTO<DinPayQueryResponseDTO> response) {
        if (response == null || response.getData() == null) {
            return;
        }
        DinPayQueryResponseDTO data = response.getData();
        String orderStatus = data.getOrderStatus();
        if (DinPaymentEnum.orderStatus.SUCCESS.getStatus().equals(orderStatus)) {
            log.info("支付单:{}主动查询支付成功，即将执行成功操作", data.getOrderNo());
            DinPayNotifyDTO dinPayNotifyDTO = DinPayNotifyDTO.builder()
                    .orderNo(data.getOrderNo())
                    .payAmount(data.getPayAmount())
                    .orderPayDate(data.getOrderPayDate())
                    .outTransactionOrderId(data.getOutTransactionOrderId())
                    .paymentMethods(data.getPaymentMethods())
                    .paymentType(data.getPaymentType())
                    .build();
            dinNotifyService.onSuccess(dinPayNotifyDTO);
            log.info("支付单:{}主动查询支付成功，成功操作执行完毕", data.getOrderNo());
            return;
        }
        log.info("支付单:{}主动查询非支付成功，订单状态:{}", data.getOrderNo(), orderStatus);
    }

    /**
     * 同步支付结果
     * @param orderInfo
     * @param <T>
     * @return
     */
    private <T extends BasePayOrderInfo> DinResponseDTO<DinPayQueryResponseDTO> commonSyncPayResult(T orderInfo) {
        DinResponseDTO<DinPayQueryResponseDTO> response = dinCommonRequest(DinPaymentEnum.url.PAY_QUERY.getUrl(), orderInfo, this::buildQueryRequestData, DinPayQueryResponseDTO.class);
        checkDinResponse(response, Collections.singletonList(DinPaymentEnum.responseCode.SUCCESS.getCode()));
        return response;
    }

    /**
     * 组装查询请求参数
     * @param t
     * @return
     * @param <T>
     */
    private <T extends BasePayOrderInfo> DinPayQueryRequestDTO buildQueryRequestData(T t) {
        return DinPayQueryRequestDTO.builder()
                .interfaceName(DinPaymentEnum.interfaceName.APP_PAY_QUERY.getName())
                .orderNo(t.getPayOrderNo())
                .build();
    }

    /**
     * 关单
     * @param queryOrderInfo 订单信息
     * @return
     */
    @Override
    public CommonStatusEnum closeOrder(BasePayOrderInfo queryOrderInfo) {
        try {
            dinCommonRequest(DinPaymentEnum.url.PAY_CLOSE.getUrl(), queryOrderInfo, this::buildCloseOrderRequestData, DinPayCloseResponseDTO.class);
        } catch (Exception e) {
            return CommonStatusEnum.FAIL;
        }
        return CommonStatusEnum.SUCCESS;
    }

    /**
     * 组装关单请求
     * @param payOrderInfo
     * @return
     */
    private DinPayCloseRequestDTO buildCloseOrderRequestData(BasePayOrderInfo payOrderInfo) {
        return DinPayCloseRequestDTO.builder()
                .interfaceName(DinPaymentEnum.interfaceName.APP_PAY_CLOSE.getName())
                .payOrderNo(payOrderInfo.getPayOrderNo())
                .build();
    }

    /**
     * 退款请求
     * @param refundOrderInfo 退款信息
     * @return
     */
    @Override
    public AjaxResult refundRequest(RefundOrderInfo refundOrderInfo) {
        log.info("开始处理智付退款请求, 退款单号:{}", refundOrderInfo.getRefund().getRefundNo());
        // 1、预处理退款事件
        refundHandleEventMapper.updateStatusByRefundNo(HandleEventStatus.IN_HANDLING.ordinal(), refundOrderInfo.getRefund().getRefundNo());
        // 2、请求退款
        DinResponseDTO<DinRefundResponseDTO> response = dinCommonRequest(DinPaymentEnum.url.REFUND.getUrl(), refundOrderInfo, this::buildRefundRequestData, DinRefundResponseDTO.class);
        checkDinResponse(response, Lists.newArrayList(DinPaymentEnum.responseCode.SUCCESS.getCode(), DinPaymentEnum.responseCode.REFUND_RECEIVE_SUCCESS.getCode()));
        // 3、处理退款结果
        return AjaxResult.getOK("退款正在处理中", P);
    }

    /**
     * 组装退款请求参数
     * @param refundOrderInfo
     * @return
     */
    private DinRefundRequestDTO buildRefundRequestData(RefundOrderInfo refundOrderInfo) {
        return DinRefundRequestDTO.builder()
                .interfaceName(DinPaymentEnum.interfaceName.APP_PAY_REFUND.getName())
                .payOrderNo(refundOrderInfo.getPayOrderNo())
                .refundOrderNo(refundOrderInfo.getRefund().getRefundNo())
                .refundAmount(refundOrderInfo.getRefund().getRefundFee().divide(new BigDecimal(100), 2, RoundingMode.HALF_UP))
                .notifyUrl(Global.DOMAIN_NAME +
                        NotifyUrl.DIN_REFUND_NOTIFY_URL)
                .build();
    }

    /**
     * 查询退款
     * @param refundOrderInfo @return 退款情况
     * @return
     */
    @Override
    public AjaxResult queryRefund(RefundOrderInfo refundOrderInfo) {
        // 请求退款查询
        String refundNo = refundOrderInfo.getRefund().getRefundNo();
        log.info("开始处理智付退款查询请求, 退款单号:{}", refundNo);
        DinResponseDTO<DinRefundQueryResponseDTO> response = dinCommonRequest(DinPaymentEnum.url.REFUND_QUERY.getUrl(), refundOrderInfo, this::buildQueryRefundRequestData, DinRefundQueryResponseDTO.class);
        // 校验退款查询结果
        checkDinResponse(response, Collections.singletonList(DinPaymentEnum.responseCode.SUCCESS.getCode()));
        // 组装返回结果
        AjaxResult result = buildResultByRefundStatus(response.getData());
        log.info("智付退款查询请求处理完毕, 退款单号:{}, 结果:{}", refundNo, result);
        return result;
    }

    /**
     * 根据退款状态组装返回结果
     * @param data
     * @return
     */
    private AjaxResult buildResultByRefundStatus(DinRefundQueryResponseDTO data) {
        String refundStatus = data.getOrderStatus();
        if (DinPaymentEnum.refundStatus.SUCCESS.getStatus().equals(refundStatus)) {
            return AjaxResult.getOK("退款成功", RefundReponseEnum.S);
        }
        if (DinPaymentEnum.refundStatus.FAIL.getStatus().equals(refundStatus) || DinPaymentEnum.refundStatus.CLOSE.getStatus().equals(refundStatus)) {
            return AjaxResult.getError("退款失败");
        }
        return AjaxResult.getOK("退款处理中", P);
    }

    /**
     * 组装查询退款请求参数
     * @param refundOrderInfo
     * @return
     */
    private DinRefundQueryRequestDTO buildQueryRefundRequestData(RefundOrderInfo refundOrderInfo) {
        return DinRefundQueryRequestDTO.builder()
                .interfaceName(DinPaymentEnum.interfaceName.APP_PAY_REFUND_QUERY.getName())
                .refundOrderNo(refundOrderInfo.getRefund().getRefundNo())
                .build();
    }

    /**
     * 校验接口响应结果
     * @param response 响应
     * @param successCodes 成功code
     */
    private void checkDinResponse(DinResponseDTO<?> response, List<String> successCodes) {
        if (!successCodes.contains(response.getCode())) {
            log.error("智付请求响应异常:{}", response, new ProviderException(response.getMsg()));
            throw new ProviderException("智付请求响应异常");
        }
        if (response.getData() == null) {
            log.error("智付请求响应数据异常:{}", response, new ProviderException(response.getMsg()));
            throw new ProviderException("智付请求响应异常");
        }
    }
}
