package net.summerfarm.mall.payments.request.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSONObject;
import com.aliyun.openservices.shade.com.alibaba.fastjson.JSON;
import io.swagger.models.auth.In;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.common.AjaxResult;
import net.summerfarm.common.exceptions.DefaultServiceException;
import net.summerfarm.contexts.ResultConstant;
import net.summerfarm.enums.OrderSaleType;
import net.summerfarm.enums.OrderTypeEnum;
import net.summerfarm.enums.RefundStatusEnum;
import net.summerfarm.mall.common.util.DateUtils;
import net.summerfarm.mall.common.util.RequestHolder;
import net.summerfarm.mall.contexts.Global;
import net.summerfarm.mall.contexts.OrderPayStatusConstant;
import net.summerfarm.mall.enums.OrderStatusEnum;
import net.summerfarm.mall.enums.WxlitePayChannelEnum;
import net.summerfarm.mall.enums.payment.PaymentEnums;
import net.summerfarm.mall.mapper.AreaMapper;
import net.summerfarm.mall.mapper.OrdersMapper;
import net.summerfarm.mall.mapper.PaymentMapper;
import net.summerfarm.mall.mapper.RefundMapper;
import net.summerfarm.mall.model.domain.*;
import net.summerfarm.mall.model.input.payment.PaymentInput;
import net.summerfarm.mall.model.input.payment.PaymentOrderInput;
import net.summerfarm.mall.model.vo.payment.PayResultVO;
import net.summerfarm.mall.payments.PaymentStrategy;
import net.summerfarm.mall.payments.common.config.PaymentConfig;
import net.summerfarm.mall.payments.common.enums.BasePayTypeEnum;
import net.summerfarm.mall.payments.common.enums.PaymentBizTypeEnum;
import net.summerfarm.mall.payments.common.pojo.*;
import net.summerfarm.mall.payments.request.PaymentHandler;
import net.summerfarm.mall.payments.request.PaymentRequestService;
import net.summerfarm.mall.service.*;
import net.xianmu.common.result.CommonResult;
import net.xianmu.common.result.ResultStatusEnum;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.io.IOException;
import java.math.BigDecimal;
import java.security.KeyManagementException;
import java.security.KeyStoreException;
import java.security.NoSuchAlgorithmException;
import java.security.UnrecoverableKeyException;
import java.security.cert.CertificateException;
import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import static net.summerfarm.mall.contexts.Global.getOrderNo;
import static net.summerfarm.mall.contexts.Global.getPayTypeEnum;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @date 2021-06-28
 * @description 统一支付入口
 */
@Slf4j
@Service
public class PaymentHandlerImpl implements PaymentHandler {
    @Resource
    private OrdersMapper ordersMapper;
    @Resource
    private AreaMapper areaMapper;
    @Resource
    private RefundMapper refundMapper;
    @Resource
    private PaymentMapper paymentMapper;
    @Resource
    private PaymentStrategy paymentStrategy;
    @Resource
    private RefundService refundService;
    @Resource
    private RedisTemplate redisTemplate;
    @Resource
    private MasterOrderService masterOrderService;
    @Resource
    private OrderRelationService orderRelationService;
    @Resource
    private MasterPaymentService masterPaymentService;
    @Resource
    private PaymentConfig paymentConfig;

    /**
     * 支付类型
     */
    private static final BasePayTypeEnum[] PAY_TYPE = BasePayTypeEnum.values();


    @Override
    public String pay(PaymentOrderInput paymentOrderInput) {
        Integer payType = paymentOrderInput.getPayChannel();
        String orderNo = paymentOrderInput.getOrderNo();
        //账期支付校验
        if (Objects.equals(payType, 3)){
            if (!RequestHolder.isMajorDirect()){
                throw new DefaultServiceException("不支持该付款方式");
            }
        }
        //数据校验
        if (payType < 0 || payType > PAY_TYPE[PAY_TYPE.length - 1].ordinal()) {
            throw new DefaultServiceException(1, "支付类型异常");
        }
        Orders orders = ordersMapper.selectByOrderNo(orderNo);
        if (orders == null) {
            throw new DefaultServiceException(0, ResultConstant.RECORD_NOT_EXIST);
        }
        if (orders.getStatus() != OrderStatusEnum.NO_PAYMENT.getId()) {
            return "{\"returncode\":\"" + "10000002" + "\",\"errmsg\":\"" + "订单被取消或已经支付!" + "\"}";
        }

        //拆单场景禁止走原接口支付
        Map<String, OrderRelation> orderRelationMap = orderRelationService.queryMasterOrderNoByOrderNo(Collections.singletonList(orderNo));
        if (!orderRelationMap.isEmpty()){
            throw new DefaultServiceException("该订单暂时无法支付，请尝试重新加购下单支付！");
        }

        //支付信息处理
        PaymentOrderInfo payOrderInfo = getPayOrderInfo(orders, paymentOrderInput);

        //重复支付校验
        if (redisTemplate.hasKey(OrderPayStatusConstant.XM_PAY_REPEAT_CHECK + payOrderInfo.getPayOrderNo())){
            throw new DefaultServiceException("订单处理中，请勿重复支付");
        }

        log.info("即将支付订单{}，支付订单号：{}，价格：{}，支付类型：{}，accountId：{}", payOrderInfo.getOrderNo(), payOrderInfo.getPayOrderNo(), payOrderInfo.getPayTotalPrice(), payOrderInfo.getPayType(), payOrderInfo.getCompanyAccountId());

        //修改订单下单账号为子账号
        Orders record = new Orders();
        record.setOrderNo(orderNo);
        record.setAccountId(RequestHolder.getAccountId());
        ordersMapper.updateByOrderNoSelective(record);
        return paymentStrategy.initByOrderInfo(payOrderInfo).payRequest(payOrderInfo);
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED)
    public AjaxResult refundByAfterSaleOrderNo(String afterSaleOrderNo) {
        List<Refund> refunds = refundMapper.selectByAfterSaleOrderNo(afterSaleOrderNo);
        if (!CollectionUtils.isEmpty(refunds)){
            for (Refund refund : refunds) {
                log.info("refund={}",refund);
                if (refund.getStatus() == RefundStatusEnum.APPLY.ordinal()) {
                    refundService.afterRefund(refund);
                }
            }
            return AjaxResult.getOK();
        }
        return AjaxResult.getError();
    }

    @Override
    public void syncPaymentResult(String payOrderNo) {
        Payment query = new Payment();
        query.setOrderNo(payOrderNo);
        Payment payment = paymentMapper.selectOne(query);
        if (payment == null) {
            return;
        }
        if (Objects.equals(1, payment.getStatus())){
            log.info("订单已付款成功，不再主动查询支付结果，订单号：{}", payOrderNo);
            return;
        }

        QueryOrderInfo queryOrderInfo = new QueryOrderInfo();
        queryOrderInfo.setPayment(payment);
        queryOrderInfo.setPayOrderNo(payOrderNo);
        //订单号处理（兼容预售订单号）
        queryOrderInfo.setOrderNo(Global.getOrderNo(payOrderNo));
        queryOrderInfo.setPayTotalPrice(payment.getMoney());
        queryOrderInfo.setCompanyAccountId(payment.getCompanyAccountId());
        queryOrderInfo.setPayTypeStr(payment.getPayType());
        BasePayTypeEnum typeEnum = Global.getPayTypeEnum(payment.getPayType());
        queryOrderInfo.setPayType(typeEnum.ordinal());
        //判断是否是小程序付款
        queryOrderInfo.setIsMpPay(Objects.equals(Global.MINI_PROGRAM_PAY, payment.getPayType()));
        paymentStrategy.initByOrderInfo(queryOrderInfo).syncPayResult(queryOrderInfo);
    }

    @Override
    @Transactional
    public AjaxResult performRefund(Refund refund) {
        //refund里的orderNo和payment里的一样
        String orderNo = refund.getOrderNo();
        String payOrderNo = refund.getOrderNo();
        refund.setMasterOrderNo(orderNo);
        refund.setMasterTotalFee(refund.getTotalFee());
        Map<String, OrderRelation> relationMap = orderRelationService.queryMasterOrderNoByOrderNo(Collections.singletonList(payOrderNo));
        MasterPayment masterPayment = null;
        if (!CollectionUtils.isEmpty(relationMap)){
            masterPayment = masterPaymentService.selectByMasterPaymentNo(relationMap.get(orderNo).getMasterOrderNo());
            payOrderNo = relationMap.get(orderNo).getMasterOrderNo();
            refund.setMasterOrderNo(payOrderNo);
            refund.setMasterTotalFee(masterPayment.getMoney().multiply(BigDecimal.valueOf(100)));
        }

        //支付信息
        Payment query = new Payment();
        query.setOrderNo(orderNo);
        Payment payment = paymentMapper.selectOne(query);
        if (payment == null) {
            throw new DefaultServiceException(1, "支付信息异常");
        }

        RefundOrderInfo refundOrderInfo = new RefundOrderInfo();
        refundOrderInfo.setRefund(refund);
        refundOrderInfo.setPayment(payment);
        refundOrderInfo.setPayOrderNo(payOrderNo);
        //订单号处理（兼容预售订单号）
        refundOrderInfo.setOrderNo(getOrderNo(orderNo));
        if (!ObjectUtils.isEmpty(masterPayment)){
            refundOrderInfo.setPayTotalPrice(masterPayment.getMoney());
        }else {
            refundOrderInfo.setPayTotalPrice(payment.getMoney());
        }
        refundOrderInfo.setCompanyAccountId(payment.getCompanyAccountId());

        //判断是否是小程序付款
        refundOrderInfo.setIsMpPay(Objects.equals(Global.MINI_PROGRAM_PAY, payment.getPayType()));

        //祖传逻辑：退款0元，走鲜沐卡
        if (refund.getRefundFee().compareTo(BigDecimal.ZERO) <= 0) {
            refundOrderInfo.setPayType(BasePayTypeEnum.XIANMU.getType());
            refundOrderInfo.setPayTypeStr(Global.XIANMU_CARD);
        } else {
            refundOrderInfo.setPayTypeStr(payment.getPayType());
            BasePayTypeEnum typeEnum = getPayTypeEnum(payment.getPayType());
            refundOrderInfo.setPayType(typeEnum.ordinal());
        }
        Orders orders = ordersMapper.selectByOrderNo(orderNo);
        refundOrderInfo.setPopFlag(orders != null && Objects.equals(orders.getType(), OrderTypeEnum.POP.getId()));
        //处理退款
        AjaxResult result;
        try {
            result = paymentStrategy.initByOrderInfo(refundOrderInfo).refundRequest(refundOrderInfo);
        } catch (DefaultServiceException | IOException | KeyStoreException | CertificateException | NoSuchAlgorithmException | UnrecoverableKeyException | KeyManagementException e) {
            log.error("退款异常", e);
            if (e instanceof DefaultServiceException) {
                throw new DefaultServiceException(1, e.getMessage());
            }
            throw new DefaultServiceException(1, "退款异常");
        }
        return result;
    }

    @Override
    public AjaxResult queryRefund(String refundNo) {
        Refund refund = refundMapper.selectByRefundNo(refundNo);
        if (refund.getStatus() == RefundStatusEnum.APPLY.ordinal() || refund.getStatus() == RefundStatusEnum.IN_HANLING.ordinal() || refund.getStatus() == RefundStatusEnum.SUCCESS.ordinal()) {
            //refund里的orderNo和payment里的一样
            String payOrderNo = refund.getOrderNo();

            //支付信息
            Payment query = new Payment();
            query.setOrderNo(payOrderNo);
            Payment payment = paymentMapper.selectOne(query);
            if (payment == null) {
                throw new DefaultServiceException(1, "支付信息异常");
            }

            RefundOrderInfo refundOrderInfo = new RefundOrderInfo();
            refundOrderInfo.setRefund(refund);
            refundOrderInfo.setPayment(payment);
            refundOrderInfo.setPayOrderNo(payOrderNo);
            //订单号处理（兼容预售订单号）
            refundOrderInfo.setOrderNo(Global.getOrderNo(payOrderNo));
            refundOrderInfo.setPayTotalPrice(payment.getMoney());
            refundOrderInfo.setCompanyAccountId(payment.getCompanyAccountId());

            //判断是否是小程序付款
            refundOrderInfo.setIsMpPay(Objects.equals(Global.MINI_PROGRAM_PAY, payment.getPayType()));

            //祖传逻辑：退款0元，走鲜沐卡
            if (refund.getRefundFee().compareTo(BigDecimal.ZERO) <= 0) {
                refundOrderInfo.setPayType(BasePayTypeEnum.XIANMU.getType());
                refundOrderInfo.setPayTypeStr(Global.XIANMU_CARD);
            } else {
                refundOrderInfo.setPayTypeStr(payment.getPayType());
                BasePayTypeEnum typeEnum = Global.getPayTypeEnum(payment.getPayType());
                refundOrderInfo.setPayType(typeEnum.ordinal());
            }

            //处理退款
            return paymentStrategy.initByOrderInfo(refundOrderInfo).queryRefund(refundOrderInfo);
        }
        return AjaxResult.getError();
    }

    @Override
//    @Transactional(rollbackFor = Exception.class)
    public void closeOrder(String payOrderNo) {
        Payment query = new Payment();
        query.setOrderNo(payOrderNo);
        Payment payment = paymentMapper.selectOne(query);
        if (payment == null) {
            return;
        }

        QueryOrderInfo queryOrderInfo = new QueryOrderInfo();
        queryOrderInfo.setPayment(payment);
        queryOrderInfo.setPayOrderNo(payOrderNo);
        //订单号处理（兼容预售订单号）
        queryOrderInfo.setOrderNo(Global.getOrderNo(payOrderNo));
        queryOrderInfo.setPayTotalPrice(payment.getMoney());
        queryOrderInfo.setCompanyAccountId(payment.getCompanyAccountId());
        queryOrderInfo.setPayTypeStr(payment.getPayType());
        BasePayTypeEnum typeEnum = Global.getPayTypeEnum(payment.getPayType());
        queryOrderInfo.setPayType(typeEnum.ordinal());

        //微信支付时判断是否小程序支付
        if (typeEnum.ordinal() == BasePayTypeEnum.WEIX.ordinal()) {
            Boolean isMpPay = Objects.equals(Global.MINI_PROGRAM_PAY, payment.getPayType());
            queryOrderInfo.setIsMpPay(isMpPay);
        }

        PaymentRequestService paymentRequestService = paymentStrategy.initByOrderInfo(queryOrderInfo);

        //查询订单支付情况
        paymentRequestService.syncPayResult(queryOrderInfo);

        //再次查询结果（待支付的发起关单）
        payment = paymentMapper.selectOne(query);
        if (Objects.equals(0, payment.getStatus())) {
            paymentRequestService.closeOrder(queryOrderInfo);
        }
    }

    @Override
    public CommonResult<PayResultVO> payV2(PaymentInput input) {
        //现结大客户不可使用账期支付
        if (Objects.equals(input.getPayChannel(), 3) && !RequestHolder.isMajorDirect()) {
            return CommonResult.fail(ResultStatusEnum.BAD_REQUEST, "不支持该付款方式");
        }

        //数据校验
        if (input.getPayChannel() < 0 || input.getPayChannel() > PAY_TYPE[PAY_TYPE.length - 1].ordinal()) {
            return CommonResult.fail(ResultStatusEnum.BAD_REQUEST, "支付类型异常");
        }

        //校验主订单、子订单状态
        MasterOrder masterOrder = masterOrderService.findByMasterOrderNo(input.getMasterOrderNo());
        if (masterOrder == null ) {
            return CommonResult.fail(ResultStatusEnum.BAD_REQUEST, "订单信息异常");
        }
        List<Orders> ordersList = orderRelationService.selectOrdersByMasterOrderNo(input.getMasterOrderNo());
        if (CollectionUtils.isEmpty(ordersList)) {
            return CommonResult.fail(ResultStatusEnum.BAD_REQUEST, "订单信息异常");
        }
        boolean orderCheckFlag = ordersList.stream().anyMatch(el -> el.getStatus() != OrderStatusEnum.NO_PAYMENT.getId());
        if (masterOrder.getStatus() != OrderStatusEnum.NO_PAYMENT.getId() || orderCheckFlag) {
            return CommonResult.fail(ResultStatusEnum.BAD_REQUEST, "订单被取消或已经支付！");
        }

        //可支付时间校验，避免支付太晚导致订单状态异常
        LocalDateTime lastPayTime = masterOrder.getOrderTime().plusMinutes(30).minusSeconds(30);
        if (lastPayTime.isBefore(LocalDateTime.now())) {
            return CommonResult.fail(ResultStatusEnum.BAD_REQUEST, "订单已超时，请重新下单！");
        }

        //构建支付单信息
        PaymentOrderInfoV2 paymentOrderInfoV2 = getPayOrderInfoV2(masterOrder, ordersList, input, lastPayTime);

        //鲜沐卡重复支付校验
        if (Objects.equals(paymentOrderInfoV2.getPayType(), BasePayTypeEnum.XIANMU.getType())) {
            String xmPayKey = OrderPayStatusConstant.XM_PAY_REPEAT_CHECK + paymentOrderInfoV2.getPayOrderNo();
            if (redisTemplate.hasKey(xmPayKey)) {
                return CommonResult.fail(ResultStatusEnum.BAD_REQUEST, "订单处理中，请勿重复支付");
            }
        }

        log.info("即将支付订单{}，支付订单号：{}，价格：{}，支付类型：{}，accountId：{}", paymentOrderInfoV2.getOrderNo(), paymentOrderInfoV2.getPayOrderNo(), paymentOrderInfoV2.getPayTotalPrice(), paymentOrderInfoV2.getPayType(), paymentOrderInfoV2.getCompanyAccountId());

        //更新订单支付accountId、退款时需要支付人openId
        masterOrderService.updateAccountIdByMasterOrderNo(input.getMasterOrderNo(), RequestHolder.getAccountId());
        PayResultVO payResultVO = paymentStrategy.initByOrderInfoV2(paymentOrderInfoV2).payRequestV2(paymentOrderInfoV2);
        if (payResultVO.isPrepayFail()){
            return CommonResult.fail(ResultStatusEnum.SERVER_ERROR, payResultVO.getErrmsg());
        }
        return CommonResult.ok(payResultVO);
    }

    @Override
    public void syncPaymentResultV2(String masterPaymentNo) {
        MasterPayment masterPayment = masterPaymentService.selectByMasterPaymentNo(masterPaymentNo);
        if (masterPayment == null || PaymentEnums.PaymentStatus.SUCCESS.getStatus().equals(masterPayment.getStatus())) {
            log.info("支付单不存在或已付款成功，不再主动查询支付结果，支付单号：{}", masterPaymentNo);
            return;
        }

        QueryOrderInfoV2 orderInfoV2 = new QueryOrderInfoV2();
        orderInfoV2.setMasterPayment(masterPayment);
        orderInfoV2.setPayOrderNo(masterPaymentNo);
        orderInfoV2.setOrderNo(masterPaymentNo);
        orderInfoV2.setPayTotalPrice(masterPayment.getMoney());
        orderInfoV2.setCompanyAccountId(masterPayment.getCompanyAccountId());
        orderInfoV2.setPayTypeStr(masterPayment.getPayType());
        BasePayTypeEnum typeEnum = Global.getPayTypeEnum(masterPayment.getPayType());
        orderInfoV2.setPayType(typeEnum.ordinal());
        //判断是否是小程序付款
        orderInfoV2.setIsMpPay(Objects.equals(Global.MINI_PROGRAM_PAY, masterPayment.getPayType()));
        paymentStrategy.initByOrderInfoV2(orderInfoV2).syncPayResultV2(orderInfoV2);
    }

    @Override
//    @Transactional(rollbackFor = Exception.class)
    public void closeOrderV2(String masterPaymentNo) {
        MasterPayment masterPayment = masterPaymentService.selectByMasterPaymentNo(masterPaymentNo);
        if (masterPayment == null || !PaymentEnums.PaymentStatus.PAYING.getStatus().equals(masterPayment.getStatus())) {
            log.info("支付单不存在或状态异常，不可取消支付，单号：{}", masterPaymentNo);
            return;
        }

        QueryOrderInfoV2 orderInfoV2 = new QueryOrderInfoV2();
        orderInfoV2.setMasterPayment(masterPayment);
        orderInfoV2.setPayOrderNo(masterPaymentNo);
        orderInfoV2.setOrderNo(masterPaymentNo);
        orderInfoV2.setPayTotalPrice(masterPayment.getMoney());
        orderInfoV2.setCompanyAccountId(masterPayment.getCompanyAccountId());
        orderInfoV2.setPayTypeStr(masterPayment.getPayType());
        BasePayTypeEnum typeEnum = Global.getPayTypeEnum(masterPayment.getPayType());
        orderInfoV2.setPayType(typeEnum.ordinal());
        //判断是否是小程序付款
        orderInfoV2.setIsMpPay(Objects.equals(Global.MINI_PROGRAM_PAY, masterPayment.getPayType()));

        PaymentRequestService paymentRequestService = paymentStrategy.initByOrderInfoV2(orderInfoV2);

        //同步一次最新订单支付情况、仍未支付就关闭支付单
        paymentRequestService.syncPayResultV2(orderInfoV2);

        //再次查询结果（待支付的发起关单）
        MasterPayment masterPaymentNew = masterPaymentService.selectByMasterPaymentNo(masterPaymentNo);
        if (Objects.equals(PaymentEnums.PaymentStatus.PAYING.getStatus(), masterPaymentNew.getStatus())) {
            paymentRequestService.closeOrder(orderInfoV2);
        }
    }

    /**
     * 处理订单支付信息
     *
     * @param orders     订单信息
     * @return 订单支付信息
     */
    private PaymentOrderInfo getPayOrderInfo(Orders orders, PaymentOrderInput paymentOrderInput) {
        Integer payType = paymentOrderInput.getPayChannel();
        String bocPayType = paymentOrderInput.getPayPlatform();

        PaymentOrderInfo result = new PaymentOrderInfo();
        result.setOrders(orders);
        result.setOrderNo(orders.getOrderNo());
        result.setOrderTime(DateUtils.date2LocalDateTime(orders.getOrderTime()));

        //是否是小程序支付
        boolean isMpPay = RequestHolder.isMiniProgramLogin();
        result.setIsMpPay(isMpPay);

        if (paymentOrderInput.isRouteVersionFlag()) {
            result.setCompanyAccountId(paymentOrderInput.getCompanyAccountId());
        } else {
            //使用运营服务区配置的收款途径
            Area area = areaMapper.selectByAreaNo(orders.getAreaNo());
            Integer accountId = area.getCompanyAccountId();
            result.setCompanyAccountId(accountId);

            //账期客户强制使用账期模式支付
            if (RequestHolder.isMajorDirect()) {
                payType = BasePayTypeEnum.PERIOD.getType();
            } //处理招行小程序支付
            else if (isMpPay) {
                //前端传微信支付、且运营服务区配置使用招行
                if (BasePayTypeEnum.WEIX.getType() == payType
                        && WxlitePayChannelEnum.CMB.getWxlitePayChannel().equals(area.getWxlitePayChannel())) {
                    payType = BasePayTypeEnum.CMB.getType();
                    bocPayType = "WEIX";
                }
            }
        }

        //普通订单
        if (OrderTypeEnum.VIRTUAL_GOODS.getId().equals(orders.getType())
                || OrderSaleType.NORMAL_OR_CARD.ordinal() == orders.getOrderSaleType()) {
            result.setPayOrderNo(orders.getOrderNo());
            result.setPayTotalPrice(orders.getTotalPrice());
        } else {
            throw new DefaultServiceException(0, "订单信息错误");
        }

        //补充支付信息
        result.setPayType(payType);
        result.setOriginPayType(bocPayType);
        //微信支付时，paytype值为微信支付、小程序支付
        if (BasePayTypeEnum.WEIX.getType() == payType){
            result.setPayTypeStr(isMpPay ? Global.MINI_PROGRAM_PAY : Global.WECHAT_PAY);
        } else {
            BasePayTypeEnum[] typeEnums = BasePayTypeEnum.values();
            result.setPayTypeStr(typeEnums[payType].getTypeStr());
        }

        result.setJsCode(paymentOrderInput.getJsCode());
        result.setPaymentBizType(PaymentBizTypeEnum.ORDER_PAY.getCode());
        return result;
    }

    /**
     * 处理订单支付信息
     * @param masterOrder 主订单信息
     * @param ordersList 子订单信息
     * @param input 支付信息
     * @return 订单支付信息
     */
    private PaymentOrderInfoV2 getPayOrderInfoV2(MasterOrder masterOrder, List<Orders> ordersList, PaymentInput input, LocalDateTime lastPayTime) {
        PaymentOrderInfoV2 result = new PaymentOrderInfoV2();
        result.setMasterOrder(masterOrder);
        result.setOrdersList(ordersList);
        result.setOrderNo(masterOrder.getMasterOrderNo());
        result.setPayOrderNo(masterOrder.getMasterOrderNo());
        result.setPayTotalPrice(masterOrder.getTotalPrice());
        result.setOrderTime(masterOrder.getOrderTime());

        //是否是小程序支付
        boolean isMpPay = RequestHolder.isMiniProgramLogin();
        result.setIsMpPay(isMpPay);

        // 是否是pop订单
        boolean popOrderFlag = Objects.equals(masterOrder.getType(), OrderTypeEnum.POP.getId());
        result.setPopFlag(popOrderFlag);

        //补充支付信息
        Integer payChannel = input.getPayChannel();
        String payPlatform = input.getPayPlatform();
        if (input.isRouteVersionFlag()) {
            result.setCompanyAccountId(input.getCompanyAccountId());
        } else {
            //使用运营服务区配置的收款途径
            Area area = areaMapper.selectByAreaNo(masterOrder.getAreaNo());
            Integer accountId = area.getCompanyAccountId();
            result.setCompanyAccountId(accountId);

            //账期客户强制使用账期模式支付
            if (RequestHolder.isMajorDirect()) {
                payChannel = BasePayTypeEnum.PERIOD.getType();
            } //处理招行小程序支付
            else if (isMpPay) {
                //前端传微信支付、且运营服务区配置使用招行
                if (!popOrderFlag && BasePayTypeEnum.WEIX.getType().equals(payChannel)
                        && WxlitePayChannelEnum.CMB.getWxlitePayChannel().equals(area.getWxlitePayChannel())) {
                    payChannel = BasePayTypeEnum.CMB.getType();
                    payPlatform = "WEIX";
                }
            }
        }
        result.setPayType(payChannel);
        result.setOriginPayType(payPlatform);

        //微信支付时，paytype值为微信支付、小程序支付
        if (BasePayTypeEnum.WEIX.getType().equals(payChannel)){
            result.setPayTypeStr(isMpPay ? Global.MINI_PROGRAM_PAY : Global.WECHAT_PAY);
        } else {
            BasePayTypeEnum[] typeEnums = BasePayTypeEnum.values();
            result.setPayTypeStr(typeEnums[payChannel].getTypeStr());
        }

        result.setJsCode(input.getJsCode());
        result.setPaymentBizType(PaymentBizTypeEnum.MASTER_ORDER_PAY.getCode());
        return result;
    }
}
