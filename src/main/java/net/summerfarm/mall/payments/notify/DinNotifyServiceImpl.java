package net.summerfarm.mall.payments.notify;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.enums.RefundStatusEnum;
import net.summerfarm.mall.common.mq.MQData;
import net.summerfarm.mall.common.mq.MType;
import net.summerfarm.mall.contexts.MQTopicConstant;
import net.summerfarm.mall.enums.payment.PaymentEnums;
import net.summerfarm.mall.mapper.CompanyAccountMapper;
import net.summerfarm.mall.mapper.OrdersMapper;
import net.summerfarm.mall.mapper.PaymentMapper;
import net.summerfarm.mall.mapper.RefundMapper;
import net.summerfarm.mall.model.domain.*;
import net.summerfarm.mall.payments.common.enums.BasePayTypeEnum;
import net.summerfarm.mall.payments.common.enums.DinPaymentEnum;
import net.summerfarm.mall.payments.common.pojo.PaymentAttachInfoDTO;
import net.summerfarm.mall.payments.common.pojo.din.DinPayConfig;
import net.summerfarm.mall.payments.common.pojo.din.notify.DinNotifyDTO;
import net.summerfarm.mall.payments.common.pojo.din.notify.DinPayNotifyDTO;
import net.summerfarm.mall.payments.common.pojo.din.notify.DinRefundNotifyDTO;
import net.summerfarm.mall.payments.common.utils.CertUtils;
import net.summerfarm.mall.payments.common.utils.PaymentNoGenerator;
import net.summerfarm.mall.payments.common.utils.SM2Utils;
import net.summerfarm.mall.service.MasterOrderService;
import net.summerfarm.mall.service.OrderRelationService;
import net.xianmu.common.exception.BizException;
import net.xianmu.common.exception.ParamsException;
import net.xianmu.rocketmq.support.producer.MqProducer;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.security.PublicKey;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.Date;
import java.util.List;

/**
 * @description:
 * @author: George
 * @date: 2025-02-11
 **/
@Service
@Slf4j
public class DinNotifyServiceImpl implements DinNotifyService {

    @Resource
    private CompanyAccountMapper companyAccountMapper;
    @Resource
    private OrderRelationService orderRelationService;
    @Resource
    private OrdersMapper ordersMapper;
    @Resource
    private MqProducer mqProducer;
    @Resource
    private MasterOrderService masterOrderService;
    @Resource
    private Notify2MQHandler notify2MQHandler;
    @Resource
    private RefundMapper refundMapper;
    @Resource
    private PaymentMapper paymentMapper;

    @Override
    public String payNotify(DinNotifyDTO dinNotifyDTO) {
        log.info("收到智付支付回调:{}", dinNotifyDTO);

        // 校验回调数据
        validateNotifyParams(dinNotifyDTO);

        // 解析数据
        DinPayNotifyDTO data = parseData(dinNotifyDTO.getData(), DinPayNotifyDTO.class);

        // 解析密钥配置信息
        DinPayConfig dinPayConfig = parseSecretKey(data);

        // 验签
        validateSign(dinPayConfig, dinNotifyDTO.getData(), dinNotifyDTO.getSign());

        // 旧机器兼容
        String orderNo = data.getOrderNo();
        if (orderNo.startsWith(PaymentNoGenerator.PAYMENT_NO_PREFIX)) {
            log.warn("旧机器接收到了新支付单号回调:{}，即将转定时任务进行补偿", orderNo);
            return DinPaymentEnum.responseCode.NOTIFY_PROCESS_SUCCESS.getCode();
        }

        // 业务处理
        onSuccessOrFail(data);

        log.info("支付单:{}智付回调处理成功", data.getOrderNo());
        // 返回结果
        return DinPaymentEnum.responseCode.NOTIFY_PROCESS_SUCCESS.getCode();
    }

    /**
     * 从订单备注字段解析出原交易时的密钥信息
     * 避免密钥紊乱
     * @param data
     * @return
     */
    private DinPayConfig parseSecretKey(DinPayNotifyDTO data) {
        String orderDesc = data.getOrderDesc();
        PaymentAttachInfoDTO attachInfoDTO = JSONObject.parseObject(orderDesc, PaymentAttachInfoDTO.class);
        Integer companyAccountId = attachInfoDTO.getCompanyAccountId();
        CompanyAccount companyAccount = companyAccountMapper.selectByPrimaryKey(companyAccountId);
        if (companyAccount == null) {
            throw new ParamsException("密钥信息不合法");
        }
        String accountInfo = companyAccount.getWxAccountInfo();
        return JSONObject.parseObject(accountInfo, DinPayConfig.class);
    }

    private void onSuccessOrFail(DinPayNotifyDTO data) {
        // 失败处理
        onFail(data);
        // 成功处理
        onSuccess(data);
    }

    @Override
    public void onSuccess(DinPayNotifyDTO data) {
        if (!DinPaymentEnum.orderStatus.SUCCESS.getStatus().equals(data.getOrderStatus())) {
            return;
        }
        String orderNo = data.getOrderNo();
        List<Orders> orders = orderRelationService.selectOrdersByMasterOrderNo(orderNo);
        if (CollectionUtils.isEmpty(orders)) {
            // 子单模型
            orderSuccess(data);
        } else {
            // 主单模型
            masterOrderSuccess(data);
        }
    }

    private void orderSuccess(DinPayNotifyDTO data) {
        Payment payment = new Payment();
        payment.setPayType(BasePayTypeEnum.DIN_PAY.getTypeStr());
        payment.setTradeType(data.getWxTradeType());
        String bocType = String.format("%s(%s)", data.getPaymentType(), data.getPaymentMethods());
        payment.setBocPayType(bocType);
        payment.setOrderNo(data.getOrderNo());
        payment.setTransactionNumber(data.getOutTransactionOrderId());
        payment.setMoney(data.getPayAmount());
        payment.setEndTime(new Date());
        payment.setStatus(PaymentEnums.PaymentStatus.SUCCESS.getStatus());
        payment.setOnlinePayEndTime(DateUtil.parse(data.getOrderPayDate()));

        Orders orders = ordersMapper.selectByOrderNo(data.getOrderNo());
        notify2MQHandler.notifyOrderPaySuccess(orders, payment);
    }

    private void masterOrderSuccess(DinPayNotifyDTO data) {
        MasterPayment masterPayment = new MasterPayment();
        masterPayment.setMasterOrderNo(data.getOrderNo());
        masterPayment.setPayType(BasePayTypeEnum.DIN_PAY.getTypeStr());
        masterPayment.setTradeType(data.getWxTradeType());
        String bocType = String.format("%s(%s)", data.getPaymentType(), data.getPaymentMethods());
        masterPayment.setBocPayType(bocType);
        masterPayment.setTransactionNumber(data.getOutTransactionOrderId());
        masterPayment.setMoney(data.getPayAmount());
        masterPayment.setEndTime(LocalDateTime.now());
        masterPayment.setStatus(PaymentEnums.PaymentStatus.SUCCESS.getStatus());
        LocalDateTime onlinePayEndTime = DateUtil.parse(data.getOrderPayDate()).toInstant()
                .atZone(ZoneId.systemDefault()).toLocalDateTime();
        masterPayment.setOnlinePayEndTime(onlinePayEndTime);
        //查询主单信息
        MasterOrder masterOrder = masterOrderService.findByMasterOrderNo(data.getOrderNo());
        notify2MQHandler.notifyMasterOrderPaySuccess(masterOrder, masterPayment);
    }

    private void onFail(DinPayNotifyDTO data) {
        String orderNo = data.getOrderNo();
        if (!DinPaymentEnum.orderStatus.SUCCESS.getStatus().equals(data.getOrderStatus())) {
            log.error("支付单:{}支付失败", orderNo);
        }
    }

    /**
     * 验签
     * @param dinPayConfig 平台公钥
     * @param data 数据
     * @param sign 签名
     */
    private void validateSign(DinPayConfig dinPayConfig, String data, String sign) {
        String publicKey = dinPayConfig.getPublicKey();
        PublicKey platformPublicKey = CertUtils.getPublicKeyByBase64(publicKey);

        boolean verify = SM2Utils.verify(platformPublicKey, data, sign);
        if (!verify) {
             throw new ParamsException("智付回调验签失败");
        }
        log.info("智付回调验签成功");
    }

    /**
     * 校验回调参数
     * @param dinNotifyDTO
     */
    private void validateNotifyParams(DinNotifyDTO dinNotifyDTO) {
        if (dinNotifyDTO == null) {
            throw new ParamsException("智付回调参数为空");
        }
        if (dinNotifyDTO.getData() == null) {
            throw new ParamsException("智付回调参数data为空");
        }
        if (dinNotifyDTO.getMerchantId() == null) {
            throw new ParamsException("智付回调参数merchantId为空");
        }
        if (dinNotifyDTO.getSignatureMethod() == null) {
            throw new ParamsException("智付回调参数signatureMethod为空");
        }
        if (dinNotifyDTO.getSign() == null) {
            throw new ParamsException("智付回调参数sign为空");
        }
    }

    @Override
    public String refundNotify(DinNotifyDTO dinNotifyDTO) {
        log.info("收到智付退款回调:{}", dinNotifyDTO);

        //校验回调数据
        validateNotifyParams(dinNotifyDTO);

        //解析数据
        DinRefundNotifyDTO refundNotifyDTO = parseData(dinNotifyDTO.getData(), DinRefundNotifyDTO.class);

        //解析密钥配置信息
        DinPayConfig dinPayConfig = getDinPayConfig(refundNotifyDTO.getRefundOrderNo());

        //验签
        validateSign(dinPayConfig, dinNotifyDTO.getData(), dinNotifyDTO.getSign());

        //业务处理
        onSuccessOrFailForRefund(refundNotifyDTO);

        log.info("退款单:{}智付回调处理成功", refundNotifyDTO.getRefundOrderNo());
        //返回结果
        return DinPaymentEnum.responseCode.NOTIFY_PROCESS_SUCCESS.getCode();
    }

    /**
     * 通过退款单号获取密钥配置信息
     * 找原交易支付单的密钥信息
     * @param refundOrderNo
     * @return
     */
    private DinPayConfig getDinPayConfig(String refundOrderNo) {
        Refund refund = refundMapper.selectByRefundNo(refundOrderNo);
        if (refund == null) {
            throw new BizException("智付退款回调参数不合法");
        }
        String orderNo = refund.getOrderNo();
        Payment query = new Payment();
        query.setOrderNo(orderNo);
        Payment payment = paymentMapper.selectOne(query);
        Integer companyAccountId = payment.getCompanyAccountId();
        CompanyAccount companyAccount = companyAccountMapper.selectByPrimaryKey(companyAccountId);
        String accountInfo = companyAccount.getWxAccountInfo();
        return JSONObject.parseObject(accountInfo, DinPayConfig.class);
    }

    /**
     * 退款成功或失败处理
     * @param notifyDTO
     */
    private void onSuccessOrFailForRefund(DinRefundNotifyDTO notifyDTO) {
        String refundStatus = notifyDTO.getRefundOrderStatus();
        String refundNo = notifyDTO.getRefundOrderNo();
        Refund refund = refundMapper.selectByRefundNo(refundNo);
        if(DinPaymentEnum.refundStatus.SUCCESS.getStatus().equals(refundStatus)) {
            // 成功-完成订单
            Refund update = new Refund();
            update.setRefundId(refund.getRefundId());
            update.setRefundNo(refund.getRefundNo());
            update.setEndTime(new Date());
            update.setStatus((byte) RefundStatusEnum.SUCCESS.ordinal());
            String refundTime = notifyDTO.getRefundOrderCompleteDate();
            update.setOnlineRefundEndTime(DateUtil.parse(refundTime, DatePattern.NORM_DATETIME_PATTERN));
            update.setTransactionNumber(notifyDTO.getRefundChannelNumber());
            MQData mqData = new MQData();
            mqData.setType(MType.REFUND_NOTIFY_SUCCESS.name());
            mqData.setData(JSONObject.toJSONString(update));
            mqProducer.sendOrderly(MQTopicConstant.MALL_PAYMENT_LIST,null,JSONObject.toJSONString(mqData), refund.getOrderNo());
        } else {
            log.error("智付退款失败, 退款单号:{}", refundNo, new BizException("智付退款失败, 退款单号：" + refundNo));
        }
    }

    /**
     * 解析数据
     * @param data
     * @param clazz
     * @return
     * @param <T>
     */
    private <T> T parseData(String data, Class<T> clazz) {
        try {
            return JSONObject.parseObject(data, clazz);
        } catch (Exception e) {
            log.error("智付回调数据解析异常", e);
            throw new ParamsException("智付回调数据解析异常");
        }
    }
}
