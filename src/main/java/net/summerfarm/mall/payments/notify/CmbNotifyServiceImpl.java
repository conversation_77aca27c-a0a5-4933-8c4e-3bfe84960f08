package net.summerfarm.mall.payments.notify;

import com.alibaba.fastjson.JSONObject;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.common.exceptions.DefaultServiceException;
import net.summerfarm.enums.RefundStatusEnum;
import net.summerfarm.mall.common.mq.MQData;
import net.summerfarm.mall.common.mq.MType;
import net.summerfarm.mall.common.mq.PayNotifySuccessData;
import net.summerfarm.mall.common.mq.PayNotifySuccessDataV2;
import net.summerfarm.mall.common.util.DateUtils;
import net.summerfarm.mall.contexts.MQDelayConstant;
import net.summerfarm.mall.contexts.MQTopicConstant;
import net.summerfarm.mall.contexts.OrderPayStatusConstant;
import net.summerfarm.mall.mapper.CompanyAccountMapper;
import net.summerfarm.mall.mapper.OrdersMapper;
import net.summerfarm.mall.mapper.RefundMapper;
import net.summerfarm.mall.model.domain.*;
import net.summerfarm.mall.payments.common.delayqueue.PaymentDelayQueueItem;
import net.summerfarm.mall.payments.common.enums.BasePayTypeEnum;
import net.summerfarm.mall.payments.common.pojo.cmb.CmbPayConfig;
import net.summerfarm.mall.payments.common.utils.CmbPayUtil;
import net.summerfarm.mall.payments.common.utils.PaymentNoGenerator;
import net.summerfarm.mall.service.MasterOrderService;
import net.summerfarm.mall.wechat.utils.TenpayUtil;
import net.xianmu.rocketmq.support.producer.MqProducer;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Date;
import java.util.Map;
import java.util.Objects;
import java.util.TreeMap;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @date 2021-07-05
 * @description
 */
@Slf4j
@Service
public class CmbNotifyServiceImpl implements CmbNotifyService {
    @Resource
    private CompanyAccountMapper companyAccountMapper;
    @Resource
    private OrdersMapper ordersMapper;
    @Resource
    private StringRedisTemplate stringRedisTemplate;
    @Resource
    private RefundMapper refundMapper;
    @Resource
    private RedisTemplate redisTemplate;
    @Resource
    private MqProducer mqProducer;
    @Resource
    private MasterOrderService masterOrderService;

    @Override
//    @Transactional(rollbackFor = Exception.class)
    public Map<String, String> cmbPayNotify(Map<String, String> notifyMap, boolean isNotify) {
        if (isNotify){
            log.info("收到招行支付回调，数据信息：{}", JSONObject.toJSONString(notifyMap));
        } //反查处理
        else {
            if(!"SUCCESS".equals(notifyMap.get("returnCode"))){
                throw new DefaultServiceException(1, "招行支付订单反查异常");
            }

            //未成功支付时
            if (!"SUCCESS".equals(notifyMap.get("respCode"))) {
                return null;
            }

            JSONObject bizContent = JSONObject.parseObject(notifyMap.get("biz_content"));
            String payOrderNo = bizContent.getString("orderId");
            //处理中继续反查
            if ("P".equals(bizContent.getString("tradeState"))) {
                Boolean conFlag = stringRedisTemplate.hasKey("DELAY_PAYMENT:" + payOrderNo);
                if(conFlag == null || !conFlag){
                    return null;
                }

                PaymentDelayQueueItem delayQueueItem = new PaymentDelayQueueItem(payOrderNo, 10 * 1000L);
                try {
                    MQData payData = new MQData();
                    payData.setType(MType.PAYMENT_STATUS_QUERY.name());
                    payData.setData(JSONObject.toJSONString(delayQueueItem));
                    //producer.sendDelayDataToQueue(MQTopicConstant.MALL_PAYMENT_DELAY_LIST, JSONObject.toJSONString(payData), MQDelayConstant.THREE_DELAY_LEVEL);
                    mqProducer.sendDelay(MQTopicConstant.MALL_PAYMENT_DELAY_LIST,null, JSONObject.toJSONString(payData), MQDelayConstant.THREE_DELAY_LEVEL_LONG);
                } catch (Exception e) {
                    throw new DefaultServiceException(1, "设置订单反查失败");
                }
                return null;
            } //非成功状态不处理
            else if (!"S".equals(bizContent.getString("tradeState"))){
                log.info("订单交易终止，停止反查");
                return null;
            }
        }

        //支付账号信息
        JSONObject bizContent = JSONObject.parseObject(notifyMap.get("biz_content"));
        String mchReserved = bizContent.getString("mchReserved");
        Integer companyAccountId = Integer.valueOf(mchReserved);
        CompanyAccount account = companyAccountMapper.selectByPrimaryKey(companyAccountId);
        CmbPayConfig payConfig = JSONObject.parseObject(account.getWxAccountInfo(), CmbPayConfig.class);
        String payOrderNo = bizContent.getString("orderId");
        if (payOrderNo.startsWith(PaymentNoGenerator.PAYMENT_NO_PREFIX)) {
            log.warn("旧机器接收到了新支付单号回调:{}，即将转定时任务进行补偿", payOrderNo);
            return buildSuccessAck(payConfig.getPriKey());
        }

        //1. 验证签名
        checkSign(payConfig, notifyMap);

        //2. 支付成功处理
        String orderNo = payOrderNo.split("_")[0];
        Orders orders = ordersMapper.selectByOrderNo(orderNo);

        Payment payment = new Payment();
        payment.setPayType(BasePayTypeEnum.CMB.getTypeStr());
        payment.setOrderNo(payOrderNo);
        payment.setTransactionNumber(bizContent.getString("cmbOrderId"));
        payment.setMoney(TenpayUtil.toYuan(bizContent.getString("txnAmt")));
        payment.setEndTime(new Date());
        payment.setTradeType("JSAPI");
        payment.setBankType(bizContent.getString("payBank"));
        payment.setBocPayType(bizContent.getString("payType"));
        payment.setStatus(1);
        payment.setCompanyAccountId(companyAccountId);

        String endDate = bizContent.getString("endDate");
        String endTime = bizContent.getString("endTime");
        if (StringUtils.isNotBlank(endDate) && StringUtils.isNotBlank(endTime)){
            String d = endDate+"-"+endTime;
            SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd-HHmmss");
            try {
                Date date = sdf.parse(d);
                payment.setOnlinePayEndTime(date);
            } catch (ParseException e) {
                log.info("日期转换异常",e);
            }
        }

        PayNotifySuccessData payNotifySuccessData=new PayNotifySuccessData();
        payNotifySuccessData.setPayment(payment);
        payNotifySuccessData.setOrder(orders);
        MQData mqData = new MQData();
        mqData.setType(MType.PAYMENT_NOTIFY_SUCCESS.name());
        mqData.setData(JSONObject.toJSONString(payNotifySuccessData));
        //producer.sendOrderlyDataToQueue(MQTopicConstant.MALL_PAYMENT_LIST, JSONObject.toJSONString(mqData), orders.getOrderNo());
        mqProducer.sendOrderly(MQTopicConstant.MALL_PAYMENT_LIST,null,JSONObject.toJSONString(mqData), orders.getOrderNo());
        redisTemplate.opsForValue().set(OrderPayStatusConstant.ORDER_PAY_SUCCESS_STATUS + orders.getOrderNo(), "SUCCESS", 5, TimeUnit.SECONDS);

        //3. 返回处理成功结果
        return buildSuccessAck(payConfig.getPriKey());
    }

    @Override
    public Map<String, String> cmbPayNotifyV2(Map<String, String> notifyMap, boolean isNotify) {
        if (isNotify){
            log.info("收到招行支付回调，数据信息：{}", JSONObject.toJSONString(notifyMap));
        } //反查处理
        else {
            if(!"SUCCESS".equals(notifyMap.get("returnCode"))){
                throw new DefaultServiceException(1, "招行支付订单反查异常");
            }

            //未成功支付时
            if (!"SUCCESS".equals(notifyMap.get("respCode"))) {
                return null;
            }

            JSONObject bizContent = JSONObject.parseObject(notifyMap.get("biz_content"));
            String payOrderNo = bizContent.getString("orderId");
            //处理中继续反查
            if ("P".equals(bizContent.getString("tradeState"))) {
                Boolean conFlag = stringRedisTemplate.hasKey("DELAY_PAYMENT:" + payOrderNo);
                if (conFlag == null || !conFlag) {
                    return null;
                }

                PaymentDelayQueueItem delayQueueItem = new PaymentDelayQueueItem(payOrderNo, 10 * 1000L);
                try {
                    MQData payData = new MQData();
                    payData.setType(MType.MASTER_PAYMENT_STATUS_QUERY.name());
                    payData.setData(JSONObject.toJSONString(delayQueueItem));
                    mqProducer.sendDelay(MQTopicConstant.MALL_PAYMENT_DELAY_LIST,null, JSONObject.toJSONString(payData), MQDelayConstant.THREE_DELAY_LEVEL_LONG);
                } catch (Exception e) {
                    throw new DefaultServiceException(1, "设置订单反查失败");
                }
                return null;
            } //非成功状态不处理
            else if (!"S".equals(bizContent.getString("tradeState"))){
                log.info("订单交易终止，停止反查");
                return null;
            }
        }

        //支付账号信息
        JSONObject bizContent = JSONObject.parseObject(notifyMap.get("biz_content"));
        String mchReserved = bizContent.getString("mchReserved");
        Integer companyAccountId = Integer.valueOf(mchReserved);
        CompanyAccount account = companyAccountMapper.selectByPrimaryKey(companyAccountId);
        CmbPayConfig payConfig = JSONObject.parseObject(account.getWxAccountInfo(), CmbPayConfig.class);
        String payOrderNo = bizContent.getString("orderId");
        if (payOrderNo.startsWith(PaymentNoGenerator.PAYMENT_NO_PREFIX)) {
            log.warn("旧机器接收到了新支付单号回调:{}，即将转定时任务进行补偿", payOrderNo);
            return buildSuccessAck(payConfig.getPriKey());
        }

        //1. 验证签名
        checkSign(payConfig, notifyMap);

        //2. 支付成功处理
        MasterPayment masterPayment = new MasterPayment();
        masterPayment.setMasterOrderNo(payOrderNo);
        masterPayment.setPayType(BasePayTypeEnum.CMB.getTypeStr());
        masterPayment.setTransactionNumber(bizContent.getString("cmbOrderId"));
        masterPayment.setMoney(TenpayUtil.toYuan(bizContent.getString("txnAmt")));
        masterPayment.setEndTime(LocalDateTime.now());
        masterPayment.setTradeType("JSAPI");
        masterPayment.setBankType(bizContent.getString("payBank"));
        masterPayment.setBocPayType(bizContent.getString("payType"));
        masterPayment.setStatus(1);
        masterPayment.setCompanyAccountId(companyAccountId);
        String endDate = bizContent.getString("endDate");
        String endTime = bizContent.getString("endTime");
        if (StringUtils.isNotBlank(endDate) && StringUtils.isNotBlank(endTime)){
            String d = endDate+"-"+endTime;
            LocalDateTime payEndTime = LocalDateTime.parse(d, DateTimeFormatter.ofPattern("yyyyMMdd-HHmmss"));
            masterPayment.setOnlinePayEndTime(payEndTime);
        }

        //查询主单信息
        MasterOrder masterOrder = masterOrderService.findByMasterOrderNo(payOrderNo);

        //发送支付成功消息
        PayNotifySuccessDataV2 payNotifySuccessData = new PayNotifySuccessDataV2();
        payNotifySuccessData.setMasterOrder(masterOrder);
        payNotifySuccessData.setMasterPayment(masterPayment);
        MQData mqData = new MQData();
        mqData.setType(MType.MASTER_PAYMENT_NOTIFY_SUCCESS.name());
        mqData.setData(JSONObject.toJSONString(payNotifySuccessData));
        mqProducer.sendOrderly(MQTopicConstant.MALL_PAYMENT_LIST, null, JSONObject.toJSONString(mqData), masterOrder.getMasterOrderNo());
        redisTemplate.opsForValue().set(OrderPayStatusConstant.ORDER_PAY_SUCCESS_STATUS + masterOrder.getMasterOrderNo(), "SUCCESS", 30, TimeUnit.SECONDS);

        //3. 返回处理成功结果
        //返回信息处理
        return buildSuccessAck(payConfig.getPriKey());
    }

    public static Map<String, String> buildSuccessAck(String privateKey) {
        Map<String, String> responseMap = new TreeMap<>();
        responseMap.put(CmbPayUtil.CmbPayConstant.VERSION, CmbPayUtil.CmbPayConstant.VERSION_VALUE);
        responseMap.put(CmbPayUtil.CmbPayConstant.ENCODING, CmbPayUtil.CmbPayConstant.ENCODING_VALUE);
        responseMap.put(CmbPayUtil.CmbPayConstant.SIGN_METHOD, CmbPayUtil.CmbPayConstant.SIGN_METHOD_VALUE);
        responseMap.put("returnCode", "SUCCESS");
        responseMap.put("respCode", "SUCCESS");
        String sign = CmbPayUtil.SM2Util.sm2Sign(responseMap, privateKey);
        responseMap.put("sign", sign);
        return responseMap;
    }

    @Override
    public Map<String, String> cmbRefundNotify(Map<String, String> notifyMap, boolean isNotify) {
        if (isNotify){
            log.info("收到招行退款回调，数据信息：{}", JSONObject.toJSONString(notifyMap));
        } //反查处理
        else {
            log.info("反查招行退款，数据信息：{}", JSONObject.toJSONString(notifyMap));
            if(!"SUCCESS".equals(notifyMap.get("returnCode")) || !"SUCCESS".equals(notifyMap.get("respCode"))){
                throw new DefaultServiceException(1, "招行退款反查异常");
            }


            JSONObject bizContent = JSONObject.parseObject(notifyMap.get("biz_content"));
            if ("S".equals(bizContent.getString("tradeState"))){
                String refundNo = bizContent.getString("orderId");

                Refund update = refundMapper.selectByRefundNo(refundNo);
                Refund refund = new Refund();
                refund.setRefundNo(refundNo);
                refund.setRefundId(update.getRefundId());
                refund.setStatus((byte)1);
                String endDate = bizContent.getString("endDate");
                String endTime = bizContent.getString("endTime");
                if (StringUtils.isNotBlank(endDate) && StringUtils.isNotBlank(endTime)){
                    String d = endDate+"-"+endTime;
                    SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd-HHmmss");
                    try {
                        Date date = sdf.parse(d);
                        refund.setOnlineRefundEndTime(date);
                    } catch (ParseException e) {
                        log.error("时间处理异常：{}", e.getMessage(), e);
                    }
                }
                MQData mqData = new MQData();
                mqData.setType(MType.REFUND_NOTIFY_SUCCESS.name());
                mqData.setData(JSONObject.toJSONString(refund));
                //producer.sendOrderlyDataToQueue(MQTopicConstant.MALL_PAYMENT_LIST, JSONObject.toJSONString(mqData),update.getOrderNo());
                mqProducer.sendOrderly(MQTopicConstant.MALL_PAYMENT_LIST,null,JSONObject.toJSONString(mqData), update.getOrderNo());
            }

        }

        //支付账号信息
        JSONObject bizContent = JSONObject.parseObject(notifyMap.get("biz_content"));
        String refundNo = bizContent.getString("orderId");
        String mchReserved = bizContent.getString("mchReserved");
        String tradeState = bizContent.getString("tradeState");
        Integer companyAccountId = Integer.valueOf(mchReserved);
        CompanyAccount account = companyAccountMapper.selectByPrimaryKey(companyAccountId);
        CmbPayConfig payConfig = JSONObject.parseObject(account.getWxAccountInfo(), CmbPayConfig.class);

        Refund refund = refundMapper.selectByRefundNo(refundNo);
        //已经更新过状态，返回处理成功
        if (!Objects.equals(RefundStatusEnum.IN_HANLING.ordinal(), refund.getStatus().intValue())) {
            if (isNotify) {
                Map<String, String> responseMap = new TreeMap<>();
                responseMap.put(CmbPayUtil.CmbPayConstant.VERSION, CmbPayUtil.CmbPayConstant.VERSION_VALUE);
                responseMap.put(CmbPayUtil.CmbPayConstant.ENCODING, CmbPayUtil.CmbPayConstant.ENCODING_VALUE);
                responseMap.put(CmbPayUtil.CmbPayConstant.SIGN_METHOD, CmbPayUtil.CmbPayConstant.SIGN_METHOD_VALUE);
                responseMap.put("returnCode", "SUCCESS");
                responseMap.put("respCode", "SUCCESS");
                String sign = CmbPayUtil.SM2Util.sm2Sign(responseMap, payConfig.getPriKey());
                responseMap.put("sign", sign);

                return responseMap;
            }
            return null;
        }

        //1. 验证签名
        checkSign(payConfig, notifyMap);

        //2. 支付成功处理
        //移除反查
        /*RefundDelayQueueItem delayQueueItem = new RefundDelayQueueItem(refundNo, 30 * 1000L);
        Global.delayQueue.remove(delayQueueItem);*/

        if(isNotify){
            //更新退款
            Refund update = new Refund();
            update.setRefundId(refund.getRefundId());
            update.setRefundNo(refund.getRefundNo());
            update.setEndTime(new Date());
            update.setStatus((byte) RefundStatusEnum.SUCCESS.ordinal());

            String endDate = bizContent.getString("endDate");
            String endTime = bizContent.getString("endTime");
            if (StringUtils.isNotBlank(endDate) && StringUtils.isNotBlank(endTime)){
                String d = endDate+"-"+endTime;
                SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd-HHmmss");
                try {
                    Date date = sdf.parse(d);
                    update.setOnlineRefundEndTime(date);
                } catch (ParseException e) {
                    log.info("日期转换失败", e);
                }
            }
            MQData mqData = new MQData();
            mqData.setType(MType.REFUND_NOTIFY_SUCCESS.name());
            mqData.setData(JSONObject.toJSONString(update));

            //producer.sendOrderlyDataToQueue(MQTopicConstant.MALL_PAYMENT_LIST, JSONObject.toJSONString(mqData),refund.getOrderNo());
            mqProducer.sendOrderly(MQTopicConstant.MALL_PAYMENT_LIST,null,JSONObject.toJSONString(mqData), refund.getOrderNo());
        }

        //3. 返回处理成功结果
        Map<String, String> responseMap = new TreeMap<>();
        responseMap.put(CmbPayUtil.CmbPayConstant.VERSION, CmbPayUtil.CmbPayConstant.VERSION_VALUE);
        responseMap.put(CmbPayUtil.CmbPayConstant.ENCODING, CmbPayUtil.CmbPayConstant.ENCODING_VALUE);
        responseMap.put(CmbPayUtil.CmbPayConstant.SIGN_METHOD, CmbPayUtil.CmbPayConstant.SIGN_METHOD_VALUE);
        responseMap.put("returnCode", "SUCCESS");
        responseMap.put("respCode", "SUCCESS");
        String sign = CmbPayUtil.SM2Util.sm2Sign(responseMap, payConfig.getPriKey());
        responseMap.put("sign", sign);

        return responseMap;
    }

    /**
     * 签名校验
     *
     * @param payConfig 配置
     * @param result    响应数据
     */
    private void checkSign(CmbPayConfig payConfig, Map<String, String> result) {
        boolean rsaCheck;
        String resultSign;
        resultSign = result.remove("sign");
        rsaCheck = CmbPayUtil.SM2Util.sm2Check(result, resultSign, payConfig.getPubKey());
        if (!rsaCheck) {
            throw new DefaultServiceException(1, "签名验证失败");
        }
    }
}
