package net.summerfarm.mall.payments.notify;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.lianok.docking.notification.OrderNotification;
import com.lianok.docking.notification.RefundNotification;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.common.AjaxResult;
import net.summerfarm.common.util.StringUtils;
import net.summerfarm.enums.RefundStatusEnum;
import net.summerfarm.mall.common.mq.MQData;
import net.summerfarm.mall.common.mq.MType;
import net.summerfarm.mall.common.mq.PayNotifySuccessData;
import net.summerfarm.mall.common.mq.PayNotifySuccessDataV2;
import net.summerfarm.mall.common.util.FireFaceUtils;
import net.summerfarm.mall.contexts.MQTopicConstant;
import net.summerfarm.mall.mapper.CompanyAccountMapper;
import net.summerfarm.mall.mapper.OrdersMapper;
import net.summerfarm.mall.mapper.RefundMapper;
import net.summerfarm.mall.model.domain.*;
import net.summerfarm.mall.payments.common.enums.BasePayTypeEnum;
import net.summerfarm.mall.payments.common.enums.FireFaceEnum;
import net.summerfarm.mall.payments.common.enums.FireFacePaymentEnum;
import net.summerfarm.mall.payments.common.pojo.fireface.FireFaceNotifyDTO;
import net.summerfarm.mall.payments.common.utils.PaymentNoGenerator;
import net.summerfarm.mall.service.MasterOrderService;
import net.summerfarm.mall.service.OrderRelationService;
import net.xianmu.common.exception.BizException;
import net.xianmu.common.exception.ParamsException;
import net.xianmu.common.exception.ProviderException;
import net.xianmu.rocketmq.support.producer.MqProducer;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;

/**
 * @description:
 * @author: George
 * @date: 2025-01-13
 **/
@Slf4j
@Service
public class FireFaceNotifyServiceImpl implements FireFaceNotifyService {

    @Resource
    private CompanyAccountMapper companyAccountMapper;
    @Resource
    private OrderRelationService orderRelationService;
    @Resource
    private MasterOrderService masterOrderService;
    @Resource
    private MqProducer mqProducer;
    @Resource
    private OrdersMapper ordersMapper;
    @Resource
    private RefundMapper refundMapper;
    @Resource
    private Notify2MQHandler notify2MQHandler;

    @Override
    public String payNotify(Integer companyAccountId, FireFaceNotifyDTO notifyDTO) {
        // 校验数据合法性
        validateNotifyData(companyAccountId, notifyDTO);
        // 检验签名
        validateSign(companyAccountId, notifyDTO);
        // 成功或者失败处理
        OrderNotification orderNotification = JSONObject.parseObject(notifyDTO.getRespBody(), OrderNotification.class);
        String businessOrderNo = orderNotification.getBusinessOrderNo();
        if (businessOrderNo.startsWith(PaymentNoGenerator.PAYMENT_NO_PREFIX)) {
            log.warn("旧机器接收到了新支付单号回调:{}，即将转定时任务进行补偿", businessOrderNo);
            return FireFacePaymentEnum.responseCode.PAY_NOTIFY_PROCESS_SUCCESS.getCode();
        }
        // 成功失败处理
        onSuccessOrFail(orderNotification);
        // 返回成功
        return FireFacePaymentEnum.responseCode.PAY_NOTIFY_PROCESS_SUCCESS.getCode();
    }

    @Override
    public String fireFaceRefundNotify(Integer companyAccountId, FireFaceNotifyDTO notifyDTO) {
        log.info("接收到火脸退款回调：companyAccountId:{}, notifyDTO:{}。", companyAccountId, JSON.toJSONString(notifyDTO));
        // 检验签名
        validateSign(companyAccountId, notifyDTO);
        // 成功或者失败处理
        onSuccessOrFailForRefund(notifyDTO);
        return FireFacePaymentEnum.responseCode.REFUND_NOTIFY_PROCESS_SUCCESS.getCode();
    }

    private void onSuccessOrFailForRefund(FireFaceNotifyDTO notifyDTO) {
        RefundNotification refundNotification = JSONObject.parseObject(notifyDTO.getRespBody(), RefundNotification.class);
        Integer refundStatus = refundNotification.getRefundStatus();
        String businessRefundNo = refundNotification.getBusinessRefundNo();
        Refund refund = refundMapper.selectByRefundNo(businessRefundNo);
        if(FireFaceEnum.RefundStatus.REFUND_SUCCESS.getStatus().equals(refundStatus)) {
            // 成功-完成订单
            Refund update = new Refund();
            update.setRefundId(refund.getRefundId());
            update.setRefundNo(refund.getRefundNo());
            update.setEndTime(new Date());
            update.setStatus((byte) RefundStatusEnum.SUCCESS.ordinal());
            String refundTime = refundNotification.getRefundTime();
            update.setOnlineRefundEndTime(DateUtil.parse(refundTime, DatePattern.NORM_DATETIME_PATTERN));
            MQData mqData = new MQData();
            mqData.setType(MType.REFUND_NOTIFY_SUCCESS.name());
            mqData.setData(JSONObject.toJSONString(update));
            mqProducer.sendOrderly(MQTopicConstant.MALL_PAYMENT_LIST,null,JSONObject.toJSONString(mqData), refund.getOrderNo());
        } else if(FireFaceEnum.RefundStatus.REFUND_FAILED.getStatus().equals(refundStatus)) {
            log.error("火脸退款失败, notifyDTO：{}", JSON.toJSONString(notifyDTO), new BizException("火脸退款失败!退款单号：" + businessRefundNo));
        }
    }

    private void onSuccessOrFail(OrderNotification orderNotification) {
        onFail(orderNotification);
        onSuccess(orderNotification);
    }

    @Override
    public void onSuccess(OrderNotification orderNotification) {
        String businessOrderNo = orderNotification.getBusinessOrderNo();
        List<Orders> orders = orderRelationService.selectOrdersByMasterOrderNo(businessOrderNo);
        if (CollectionUtils.isEmpty(orders)) {
            // 子单模型
            orderSuccess(orderNotification);
        } else {
            // 主单模型
            masterOrderSuccess(orderNotification);
        }
    }

    private void masterOrderSuccess(OrderNotification orderNotification) {
        MasterPayment masterPayment = new MasterPayment();
        masterPayment.setMasterOrderNo(orderNotification.getBusinessOrderNo());
        masterPayment.setPayType(BasePayTypeEnum.B2B_WECHAT_FIRE_FACE.getTypeStr());
        masterPayment.setTransactionNumber(orderNotification.getTopChannelOrderNo());
        masterPayment.setMoney(orderNotification.getTotalAmount());
        masterPayment.setEndTime(LocalDateTime.now());
        masterPayment.setStatus(1);
        LocalDateTime onlinePayEndTime = DateUtil.parse(orderNotification.getPayTime()).toInstant()
                .atZone(ZoneId.systemDefault()).toLocalDateTime();
        masterPayment.setOnlinePayEndTime(onlinePayEndTime);
        //查询主单信息
        MasterOrder masterOrder = masterOrderService.findByMasterOrderNo(orderNotification.getBusinessOrderNo());
        notify2MQHandler.notifyMasterOrderPaySuccess(masterOrder, masterPayment);
    }

    private void orderSuccess(OrderNotification orderNotification) {
        Payment payment = new Payment();
        payment.setPayType(BasePayTypeEnum.B2B_WECHAT_FIRE_FACE.getTypeStr());
        payment.setOrderNo(orderNotification.getBusinessOrderNo());
        payment.setTransactionNumber(orderNotification.getTopChannelOrderNo());
        payment.setMoney(orderNotification.getTotalAmount());
        payment.setEndTime(new Date());
        payment.setStatus(1);
        payment.setOnlinePayEndTime(DateUtil.parse(orderNotification.getPayTime()));

        Orders orders = ordersMapper.selectByOrderNo(orderNotification.getBusinessOrderNo());
        notify2MQHandler.notifyOrderPaySuccess(orders, payment);
    }

    private void onFail(OrderNotification orderNotification) {
        String businessOrderNo = orderNotification.getBusinessOrderNo();
        if (!FireFacePaymentEnum.orderStatus.SUCCESS.getStatus().equals(orderNotification.getOrderStatus())) {
            // 这里和火脸确认  只有成功的订单才会回调 这里不会有失败的订单
            log.error("支付单:{}非支付成功", businessOrderNo);
        }
    }

    private void validateSign(Integer companyAccountId, FireFaceNotifyDTO notifyDTO) {
        CompanyAccount companyAccount = companyAccountMapper.selectByPrimaryKey(companyAccountId);
        if (companyAccount == null) {
            throw new ProviderException("密钥信息不合法");
        }
        String wxAccountInfo = companyAccount.getWxAccountInfo();
        JSONObject jsonObject = JSONObject.parseObject(wxAccountInfo);
        String secret = jsonObject.getString("secret");
        boolean legal = FireFaceUtils.validateSignature(notifyDTO, secret, notifyDTO.getSign());
        if (!legal) {
            throw new ProviderException("签名验证失败");
        }
    }

    private void validateNotifyData(Integer companyAccountId, FireFaceNotifyDTO notifyDTO) {
        if (notifyDTO == null) {
            throw new ParamsException("火脸支付回调参数为空");
        }
        if (Objects.isNull(companyAccountId)) {
            throw new ParamsException("火脸支付回调参数公司账号为空");
        }
        if (!FireFacePaymentEnum.responseCode.PAY_NOTIFY_SUCCESS.getCode().equals(notifyDTO.getCode())) {
            throw new ParamsException("火脸支付回调参数状态码不为200");
        }
        String respBody = notifyDTO.getRespBody();
        if (StringUtils.isBlank(respBody)) {
            throw new ParamsException("火脸支付回调参数为空");
        }
        OrderNotification orderNotification = JSONObject.parseObject(respBody, OrderNotification.class);
        if (orderNotification == null) {
            throw new ParamsException("火脸支付回调参数数据为空");
        }
        if (StringUtils.isEmpty(orderNotification.getBusinessOrderNo())) {
            throw new ParamsException("火脸支付回调参数业务单号为空");
        }
        if (Objects.isNull(orderNotification.getOrderStatus())) {
            throw new ParamsException("火脸支付回调参数订单状态为空");
        }
    }
}
