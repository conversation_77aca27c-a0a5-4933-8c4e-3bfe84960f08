package net.summerfarm.mall.payments.notify;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.common.exceptions.DefaultServiceException;
import net.summerfarm.common.util.jaxb.JaxbUtil;
import net.summerfarm.enums.RefundStatusEnum;
import net.summerfarm.mall.Conf;
import net.summerfarm.mall.common.mq.MQData;
import net.summerfarm.mall.common.mq.MType;
import net.summerfarm.mall.common.mq.PayNotifySuccessData;
import net.summerfarm.mall.common.mq.PayNotifySuccessDataV2;
import net.summerfarm.mall.common.util.AesUtil;
import net.summerfarm.mall.common.util.RequestHolder;
import net.summerfarm.mall.contexts.Global;
import net.summerfarm.mall.contexts.MQTopicConstant;
import net.summerfarm.mall.contexts.OrderPayStatusConstant;
import net.summerfarm.mall.enums.OrderStatusEnum;
import net.summerfarm.mall.mapper.*;
import net.summerfarm.mall.model.domain.*;
import net.summerfarm.mall.model.vo.WxAccountInfoVO;
import net.summerfarm.mall.payments.common.pojo.PaymentAttachInfoDTO;
import net.summerfarm.mall.payments.common.utils.NotifyParamUtil;
import net.summerfarm.mall.payments.common.utils.PaymentNoGenerator;
import net.summerfarm.mall.service.MasterOrderService;
import net.summerfarm.mall.service.MasterPaymentService;
import net.summerfarm.mall.wechat.model.Return2WX;
import net.summerfarm.mall.wechat.templatemessage.OrderPayFailMsg;
import net.summerfarm.mall.wechat.templatemessage.TemplateMsgSender;
import net.summerfarm.mall.wechat.utils.TenpayUtil;
import net.summerfarm.mall.wechat.utils.XMLUtil;
import net.xianmu.rocketmq.support.producer.MqProducer;
import org.apache.commons.lang3.StringUtils;
import org.omg.PortableInterceptor.SUCCESSFUL;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.math.BigDecimal;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @date 2021-07-05
 * @description
 */
@Slf4j
@Service
public class WeixNotifyServiceImpl implements WeixNotifyService {
    @Resource
    private PaymentMapper paymentMapper;
    @Resource
    private CompanyAccountMapper companyAccountMapper;
    @Resource
    private OrdersMapper ordersMapper;
    @Resource
    private TemplateMsgSender templateMsgSender;
    @Resource
    private RefundMapper refundMapper;
    @Resource
    private RedisTemplate redisTemplate;
    @Resource
    private MqProducer mqProducer;
    @Resource
    private MasterPaymentService masterPaymentService;
    @Resource
    private MasterOrderService masterOrderService;

    /**
     * 支付回调处理成功ACK
     */
    public static final String NOTIFY_SUCCESS_ACK = "<xml><return_code><![CDATA[SUCCESS]]></return_code><return_msg><![CDATA[OK]]></return_msg></xml>";

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String weixPayNotify() throws IOException {
        HttpServletRequest request = RequestHolder.getRequest();
        String resultStr = NotifyParamUtil.resultParam(request);
        log.info("回调数据为：{}", resultStr);
        Map<String, String> resultMap;
        resultMap = XMLUtil.xmlToMap(resultStr);
        //获取回调参数 E
        log.info("微信返回数据:" + resultMap);
        String result = null;
        if (CollectionUtils.isEmpty(resultMap)) {
            result = "I see U";
        } else {
            result = handleNotify(resultMap);
        }
        return result;
    }

    @Override
    public String weixPayNotifyV2() throws IOException {
        HttpServletRequest request = RequestHolder.getRequest();
        String resultStr = NotifyParamUtil.resultParam(request);
        log.info("回调数据为：{}", resultStr);
        Map<String, String> resultMap;
        resultMap = XMLUtil.xmlToMap(resultStr);
        //获取回调参数 E
        log.info("微信返回数据:" + resultMap);
        String result = null;
        if (!CollectionUtils.isEmpty(resultMap)) {
            result = handleNotifyV2(resultMap);
        }
        return result;
    }

    @Override
    public String weixRefundNotify() throws Exception {

        HttpServletRequest request = RequestHolder.getRequest();
        log.info("微信退款回调request数据为：{}", request);
        String resultStr = NotifyParamUtil.resultParam(request);
        log.info("微信退款回调resultStr数据为：{}", resultStr);
        Map<String, String> resultMap;
        resultMap = XMLUtil.xmlToMap(resultStr);
        log.info("微信退款回调数据为：{}", resultMap);

        //return_code是通信标识，通信是否成功
        String return_code = resultMap.get("return_code");
        if (!"SUCCESS".equals(return_code)) {
            // 返回错误信息写日志
            log.error(resultMap.get("return_msg"));
            // TODO 此处是否需要给微信返回消息
            return null;
        }
        //进行解密操作
        String appId = resultMap.get("sub_appid");
        if (!StringUtils.isNotBlank(appId)){
            appId = resultMap.get("appid");
        }
        String sign = null;
        List<CompanyAccount> companyAccounts = null;

        companyAccounts = companyAccountMapper.selectByAppId(appId);

        if (companyAccounts ==null || companyAccounts.size()<=0){
            companyAccounts = companyAccountMapper.selectByMchxAppId(appId);
            if (companyAccounts ==null || companyAccounts.size()<=0){
                //收到消息但是无法解密
                log.info("微信退款无法解密，APPID为："+appId);
                //通知微信服务器.异步确认成功.必写.不然会一直通知后台.八次之后就认为交易失败了。
                String return2WXOK = "<xml><return_code><![CDATA[SUCCESS]]></return_code><return_msg><![CDATA[OK]]></return_msg></xml>";
                String re = return2WXOK;
                log.info("微信退款异步确认成功返回值：" + re);
                return re;
            }
        }

        WxAccountInfoVO wxAccountInfoVO = JSON.parseObject(
                companyAccounts.get(0).getWxAccountInfo(), WxAccountInfoVO.class);
        sign = wxAccountInfoVO.getWxMchKey();

        String req_info = AesUtil.decryptData(resultMap.get("req_info"),sign);
        Map<String, String> reqInfoMap = XMLUtil.xmlToMap(req_info);
        resultMap.putAll(reqInfoMap);
        resultMap.remove("req_info");
        //获取回调参数 E
        log.info("微信返回数据:" + resultMap);
        String result = null;
        if (CollectionUtils.isEmpty(resultMap)) {
            result = "I see U";
        } else {
            result = RefundHandleNotify(resultMap);
        }
        return result;
    }

    private String RefundHandleNotify(Map<String, String> resultMap) {
        Refund refund = refundMapper.selectByRefundNo(resultMap.get("out_refund_no"));

        //result_code判断交易是否成功
        //处理返回数据
        if (!resultMap.get("refund_status").equals("SUCCESS")) {
            if (refund!=null){
                //校验失败状态是否一致
                if (refund.getStatus() != null && (byte) RefundStatusEnum.FAIL.ordinal() != refund.getStatus()){
                    // 1.在退款表中保存交易错误码
                    refund.setErrCode(resultMap.get("err_code"));
                    refund.setErrCodeDes(resultMap.get("err_code_des"));
                    refundMapper.updateByPrimaryKeySelective(refund);
                    log.info("已更新refund:{}", refund);
                }
            }
        } else {
            if (refund!=null){
                    // 退款成功信息更新
                    String transactionNumber = resultMap.get("refund_id");
                    // 1.在退款表中更新退款成功信息
                    refund.setStatus((byte) RefundStatusEnum.SUCCESS.ordinal());
                    refund.setTransactionNumber(transactionNumber);
                    refund.setEndTime(new Date());
                    refund.setTotalFee(new BigDecimal(resultMap.get("total_fee")));
                try {
                    if(net.summerfarm.common.util.StringUtils.isNotBlank(resultMap.get("success_time"))){
                        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                        Date date = sdf.parse(resultMap.get("success_time"));
                        refund.setOnlineRefundEndTime(date);
                    }
                } catch (ParseException e) {
                    log.info("时间转换失败", e);
                }
                refundMapper.updateByPrimaryKeySelective(refund);
            }
        }

        //通知微信服务器.异步确认成功.必写.不然会一直通知后台.八次之后就认为交易失败了.
        String return2WXOK = "<xml><return_code><![CDATA[SUCCESS]]></return_code><return_msg><![CDATA[OK]]></return_msg></xml>";
        String re = return2WXOK;
        log.info("微信退款异步确认成功返回值：" + re);
        return re;
    }


    @Override
    public String handleNotify(Map<String, String> resultMap) {
        //return_code是通信标识，通信是否成功
        String return_code = resultMap.get("return_code");
        if ("FAIL".equals(return_code)) {
            // 返回错误信息写日志
            log.error(resultMap.get("return_msg"));
            // TODO 此处是否需要给微信返回消息
            return null;
        }

        //通信成功，验证签名是否篡改
        String out_trade_no = resultMap.get("out_trade_no");
        String sign = resultMap.get("sign");
        String mchKey;
        String payOrderNo = out_trade_no;
        String orderNo = out_trade_no.split("_")[0];
        if (payOrderNo.startsWith(PaymentNoGenerator.PAYMENT_NO_PREFIX)) {
            log.warn("旧机器接收到了新支付单号回调:{}，即将转定时任务进行补偿", payOrderNo);
            return NOTIFY_SUCCESS_ACK;
        }

        // 从预留字段获取 否则从支付单获取
        Integer companyAccountId = getCompanyAccountId(payOrderNo, resultMap.get("attach"));
        log.info("paymentNo:{}, companyAccountId:{}", payOrderNo, companyAccountId);
        CompanyAccount companyAccount = companyAccountMapper.selectByPrimaryKey(companyAccountId);
        if (companyAccount != null) {
            WxAccountInfoVO wxAccountInfoVO = JSONObject.parseObject(companyAccount.getWxAccountInfo(), WxAccountInfoVO.class);
            mchKey = wxAccountInfoVO.getWxMchKey();
        } else {
            throw new DefaultServiceException(1, "微信配置异常");
        }

        String paySign = TenpayUtil.getSign(resultMap, mchKey);
        if (!sign.equals(paySign)) {
            log.warn("微信支付结果通知返回数据验证签名失败!" + payOrderNo + "sign" + sign + "paySign" + paySign);
            return JaxbUtil.beanToXml(new Return2WX("FAIL", "签名失败"));
        }

        //result_code判断交易是否成功
        String result_code = resultMap.get("result_code");
        if ("FAIL".equals(result_code)) {
            // 1.支付失败信息更新
            String appid = resultMap.get("appid");
            String openid = resultMap.get("openid");
            BigDecimal total_fee = TenpayUtil.toYuan(resultMap.get("total_fee"));

            Payment payment = new Payment();
            if (Conf.MP_APP_ID.equals(appid)) {
                payment.setPayType(Global.MINI_PROGRAM_PAY);
            } else {
                payment.setPayType(Global.WECHAT_PAY);
            }
            payment.setOrderNo(payOrderNo);
            payment.setEndTime(new Date());
            payment.setStatus(2);
            payment.setErrCode(resultMap.get("err_code"));
            payment.setErrCodeDes(resultMap.get("err_code_des"));
            payment.setCompanyAccountId(companyAccount.getId());
            paymentMapper.updateByOrderNo(payment);

            //2.更新订单状态为支付失败
            Orders orders = new Orders();
            orders.setStatus(Short.valueOf("9"));
            orders.setOrderNo(orderNo);
            ordersMapper.updateByOrderNoSelective(orders);

            //3.发送支付失败微信消息
            String msg = OrderPayFailMsg.templateMessage(openid, out_trade_no, total_fee.toString());
            templateMsgSender.sendTemplateMsg(msg);
            log.info("支付失败" + msg);
            return JaxbUtil.beanToXml(new Return2WX());
        }
        if ("SUCCESS".equals(result_code)) {
            Payment payment = new Payment();
            if (Conf.MP_APP_ID.equals(resultMap.get("appid"))) {
                payment.setPayType(Global.MINI_PROGRAM_PAY);
            } else {
                payment.setPayType(Global.WECHAT_PAY);
            }
            payment.setOrderNo(payOrderNo);
            payment.setTransactionNumber(resultMap.get("transaction_id"));
            payment.setMoney(TenpayUtil.toYuan(resultMap.get("total_fee")));
            payment.setEndTime(new Date());
            payment.setTradeType(resultMap.get("trade_type"));
            payment.setBankType(resultMap.get("bank_type"));
            payment.setStatus(1);
            payment.setCompanyAccountId(companyAccount.getId());

            try {
                if (StringUtils.isNotBlank(resultMap.get("time_end"))){
                    SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmss");
                    Date date = sdf.parse(resultMap.get("time_end"));
                    payment.setOnlinePayEndTime(date);
                }
            } catch (ParseException e) {
                log.info("时间转换失败", e);
            }

            redisTemplate.opsForValue().set(OrderPayStatusConstant.ORDER_PAY_SUCCESS_STATUS + orderNo, "SUCCESS", 5, TimeUnit.SECONDS);
            //支付成功处理
            Orders record = ordersMapper.selectByOrderNo(orderNo);

            PayNotifySuccessData payNotifySuccessData=new PayNotifySuccessData();
            payNotifySuccessData.setPayment(payment);
            payNotifySuccessData.setOrder(record);
            MQData mqData = new MQData();
            mqData.setType(MType.PAYMENT_NOTIFY_SUCCESS.name());
            mqData.setData(JSONObject.toJSONString(payNotifySuccessData));
            //producer.sendOrderlyDataToQueue(MQTopicConstant.MALL_PAYMENT_LIST, JSONObject.toJSONString(mqData), record.getOrderNo());
            mqProducer.sendOrderly(MQTopicConstant.MALL_PAYMENT_LIST,null,JSONObject.toJSONString(mqData), record.getOrderNo());
            //通知微信服务器.异步确认成功.必写.不然会一直通知后台.八次之后就认为交易失败了.
            return NOTIFY_SUCCESS_ACK;
        }

        return null;
    }

    private Integer getCompanyAccountId(String paymentNo, String attachInfo) {
        if (StringUtils.isNotBlank(attachInfo)) {
            PaymentAttachInfoDTO paymentAttachInfoDTO = JSON.parseObject(attachInfo, PaymentAttachInfoDTO.class);
            return paymentAttachInfoDTO.getCompanyAccountId();
        }

        Payment query = new Payment();
        query.setOrderNo(paymentNo);
        Payment paymentRec = paymentMapper.selectOne(query);
        return paymentRec.getCompanyAccountId();
    }

    @Override
    public String handleNotifyV2(Map<String, String> resultMap) {
        //return_code是通信标识，通信是否成功
        String return_code = resultMap.get("return_code");
        if ("FAIL".equals(return_code)) {
            // 返回错误信息写日志
            log.error(resultMap.get("return_msg"));
            return null;
        }

        //通信成功，验证签名是否篡改
        String out_trade_no = resultMap.get("out_trade_no");
        String sign = resultMap.get("sign");
        String mchKey;
        String masterOrderNo = out_trade_no;

        if (masterOrderNo.startsWith(PaymentNoGenerator.PAYMENT_NO_PREFIX)) {
            log.warn("旧机器接收到了新支付单号回调:{}，即将转定时任务进行补偿", masterOrderNo);
            return NOTIFY_SUCCESS_ACK;
        }

        MasterPayment masterPayment = masterPaymentService.selectByMasterPaymentNo(masterOrderNo);
        if (masterPayment == null) {
            throw new DefaultServiceException(1, "支付单" + masterOrderNo + "不存在");
        }

        // 从预留字段获取 否则从支付单获取
        Integer companyAccountId = getCompanyAccountIdV2(masterPayment, resultMap.get("attach"));
        log.info("paymentNo:{}, companyAccountId:{}", masterOrderNo, companyAccountId);
        CompanyAccount companyAccount = companyAccountMapper.selectByPrimaryKey(companyAccountId);
        if (companyAccount != null) {
            WxAccountInfoVO wxAccountInfoVO = JSONObject.parseObject(companyAccount.getWxAccountInfo(), WxAccountInfoVO.class);
            mchKey = wxAccountInfoVO.getWxMchKey();
        } else {
            throw new DefaultServiceException(1, "微信配置异常");
        }

        String paySign = TenpayUtil.getSign(resultMap, mchKey);
        if (!sign.equals(paySign)) {
            log.warn("微信支付结果通知返回数据验证签名失败!" + masterOrderNo + "sign" + sign + "paySign" + paySign);
            return JaxbUtil.beanToXml(new Return2WX("FAIL", "签名失败"));
        }

        //result_code判断交易是否成功
        String result_code = resultMap.get("result_code");
        if ("FAIL".equals(result_code)) {
            // 1.支付失败信息更新
            String appid = resultMap.get("appid");
            String openid = resultMap.get("openid");
            BigDecimal total_fee = TenpayUtil.toYuan(resultMap.get("total_fee"));

            MasterPayment payment = new MasterPayment();
            if (Conf.MP_APP_ID.equals(appid) || Conf.POP_MP_APP_ID.equals(appid)) {
                payment.setPayType(Global.MINI_PROGRAM_PAY);
            } else {
                payment.setPayType(Global.WECHAT_PAY);
            }
            payment.setMasterOrderNo(masterOrderNo);
            payment.setEndTime(LocalDateTime.now());
            payment.setStatus(2);
            payment.setErrCode(resultMap.get("err_code"));
            payment.setErrCodeDes(resultMap.get("err_code_des"));
            payment.setCompanyAccountId(companyAccount.getId());
            masterPaymentService.masterPaymentNotifyHandler(masterPayment);

            //2.更新订单状态为支付失败
            masterOrderService.updateMasterOrderAndSubOrderStatus(masterOrderNo, null, OrderStatusEnum.PAY_FAILED);

            //3.发送支付失败微信消息
            String msg = OrderPayFailMsg.templateMessage(openid, out_trade_no, total_fee.toString());
            templateMsgSender.sendTemplateMsg(msg);
            log.info("支付失败" + msg);
            return JaxbUtil.beanToXml(new Return2WX());
        }
        if ("SUCCESS".equals(result_code)) {
            redisTemplate.opsForValue().set(OrderPayStatusConstant.ORDER_PAY_SUCCESS_STATUS + masterOrderNo, "SUCCESS", 30, TimeUnit.SECONDS);

            MasterPayment masterPaymentNotify = new MasterPayment();
            if (Conf.MP_APP_ID.equals(resultMap.get("appid")) || Conf.POP_MP_APP_ID.equals(resultMap.get("appid"))) {
                masterPaymentNotify.setPayType(Global.MINI_PROGRAM_PAY);
            } else {
                masterPaymentNotify.setPayType(Global.WECHAT_PAY);
            }
            masterPaymentNotify.setMasterOrderNo(masterOrderNo);
            masterPaymentNotify.setTransactionNumber(resultMap.get("transaction_id"));
            masterPaymentNotify.setMoney(TenpayUtil.toYuan(resultMap.get("total_fee")));
            masterPaymentNotify.setEndTime(LocalDateTime.now());
            masterPaymentNotify.setTradeType(resultMap.get("trade_type"));
            masterPaymentNotify.setBankType(resultMap.get("bank_type"));
            masterPaymentNotify.setStatus(1);
            masterPaymentNotify.setCompanyAccountId(companyAccount.getId());
            if (StringUtils.isNotBlank(resultMap.get("time_end"))){
                masterPaymentNotify.setOnlinePayEndTime(LocalDateTime.parse(resultMap.get("time_end"), DateTimeFormatter.ofPattern("yyyyMMddHHmmss")));
            }

            MasterOrder masterOrder = masterOrderService.findByMasterOrderNo(masterOrderNo);

            //发送回调处理mq消息
            PayNotifySuccessDataV2 successDataV2 = new PayNotifySuccessDataV2();
            successDataV2.setMasterPayment(masterPaymentNotify);
            successDataV2.setMasterOrder(masterOrder);
            MQData mqData = new MQData();
            mqData.setType(MType.MASTER_PAYMENT_NOTIFY_SUCCESS.name());
            mqData.setData(JSONObject.toJSONString(successDataV2));
            mqProducer.sendOrderly(MQTopicConstant.MALL_PAYMENT_LIST,null,JSONObject.toJSONString(mqData), masterOrderNo);

            //通知微信服务器.异步确认成功.必写.不然会一直通知后台.八次之后就认为交易失败了.
            return  "<xml><return_code><![CDATA[SUCCESS]]></return_code><return_msg><![CDATA[OK]]></return_msg></xml>";
        }

        return null;
    }

    private Integer getCompanyAccountIdV2(MasterPayment masterPayment, String attach) {
        if (StringUtils.isNotBlank(attach)) {
            PaymentAttachInfoDTO paymentAttachInfoDTO = JSON.parseObject(attach, PaymentAttachInfoDTO.class);
            return paymentAttachInfoDTO.getCompanyAccountId();
        }
        return masterPayment.getCompanyAccountId();
    }
}
