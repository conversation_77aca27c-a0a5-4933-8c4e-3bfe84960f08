<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="net.summerfarm.mall.mapper.AfterSaleOrderMapper" >
  <resultMap id="BaseResultMap" type="net.summerfarm.mall.model.domain.AfterSaleOrder" >
    <id column="id" property="id" jdbcType="INTEGER" />
    <result column="after_sale_order_no" property="afterSaleOrderNo" jdbcType="VARCHAR" />
    <result column="order_no" property="orderNo" jdbcType="VARCHAR" />
    <result column="m_id" property="mId" jdbcType="BIGINT" />
    <result column="account_id" property="accountId" jdbcType="BIGINT"/>
    <result column="sku" property="sku" jdbcType="VARCHAR" />
    <result column="status" property="status" jdbcType="INTEGER" />
    <result column="after_sale_unit" property="afterSaleUnit" jdbcType="VARCHAR" />
    <result column="add_time" property="addTime" jdbcType="TIMESTAMP" />
    <result column="type" property="type" jdbcType="INTEGER" />
    <result column="grade" property="grade" jdbcType="INTEGER" />
    <result column="suit_id" property="suitId" jdbcType="INTEGER" />
  </resultMap>
  <sql id="Base_Column_List" >
    id, after_sale_order_no, m_id, account_id, order_no, sku, quantity, after_sale_type, proof_pic, handle_type,
    handle_num, apply_remark, status, handle_remark, after_sale_unit, updatetime, add_time,type,grade
  </sql>

  <resultMap id="OrderProofMap" type="net.summerfarm.mall.model.vo.AfterSaleOrderVO">
    <id column="id" property="id" jdbcType="INTEGER" />
    <result column="after_sale_order_no" property="afterSaleOrderNo" jdbcType="VARCHAR" />
    <result column="order_no" property="orderNo" jdbcType="VARCHAR" />
    <result column="m_id" property="mId" jdbcType="BIGINT" />
    <result column="account_id" property="accountId" jdbcType="BIGINT"/>
    <result column="sku" property="sku" jdbcType="VARCHAR" />
    <result column="status" property="status" jdbcType="INTEGER" />
    <result column="after_sale_unit" property="afterSaleUnit" jdbcType="VARCHAR" />
    <result column="add_time" property="addTime" jdbcType="TIMESTAMP" />
    <result column="type" property="type" jdbcType="INTEGER" />
    <result column="grade" property="grade" jdbcType="INTEGER" />
    <result column="suit_id" property="suitId" jdbcType="INTEGER" />
    <result column="quantity" property="quantity"/>
    <result column="handle_type" property="handleType"/>
    <result column="deliveryed" property="deliveryed"/>
    <result column="handle_num" property="handleNum"/>
    <result column="recovery_num" property="recoveryNum"/>
    <collection property="afterSaleProofList" ofType="net.summerfarm.mall.model.domain.AfterSaleProof">
      <result column="quantity" property="quantity"/>
      <result column="handle_type" property="handleType"/>
      <result column="handle_num" property="handleNum"/>
    </collection>
  </resultMap>

  <insert id="insertSelective" parameterType="net.summerfarm.mall.model.domain.AfterSaleOrder" >
    insert into after_sale_order
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="mId != null" >
        m_id,
      </if>
      <if test="accountId != null">
        account_id,
      </if>
      <if test="afterSaleOrderNo != null" >
        after_sale_order_no,
      </if>
      <if test="orderNo != null" >
        order_no,
      </if>
      <if test="sku != null" >
        sku,
      </if>
      <if test="status != null" >
        status,
      </if>
      <if test="afterSaleUnit != null" >
        after_sale_unit,
      </if>
      <if test="addTime != null" >
        add_time,
      </if>
      <if test="type != null">
        type,
      </if>
      <if test="grade != null">
        grade,
      </if>
      <if test="deliveryed != null">
        deliveryed,
      </if>
      <if test="suitId != null">
        suit_id,
      </if>
      <if test="times != null">
        times,
      </if>
      <if test="view != null">
        view,
      </if>
      <if test="isFull != null">
        is_full,
      </if>
      <if test="deliveryId != null">
        delivery_id,
      </if>
      <if test="recoveryType != null">
        recovery_type,
      </if>
      <if test="afterSaleRemarkType != null">
        after_sale_remark_type,
      </if>
      <if test="afterSaleRemark != null">
        after_sale_remark,
      </if>
      <if test="afterSaleOrderStatus != null">
        after_sale_order_status,
      </if>
      <if test="productType != null">
        product_type,
      </if>
      <if test="refundFreight != null">
        refund_freight,
      </if>
      <if test="carryingGoods != null">
        carrying_goods,
      </if>
      <if test="snapshot != null">
        snapshot,
      </if>
      <if test="autoAfterSaleFlag != null">
        auto_after_sale_flag,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="mId != null" >
        #{mId},
      </if>
      <if test="accountId != null">
        #{accountId},
      </if>
      <if test="afterSaleOrderNo != null" >
        #{afterSaleOrderNo,jdbcType=VARCHAR},
      </if>
      <if test="orderNo != null" >
        #{orderNo,jdbcType=VARCHAR},
      </if>
      <if test="sku != null" >
        #{sku,jdbcType=VARCHAR},
      </if>
      <if test="status != null" >
        #{status,jdbcType=INTEGER},
      </if>
      <if test="afterSaleUnit != null" >
        #{afterSaleUnit,jdbcType=VARCHAR},
      </if>
      <if test="addTime != null" >
        #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="type != null">
        #{type,jdbcType=INTEGER},
      </if>
      <if test="grade != null">
        #{grade,jdbcType=INTEGER},
      </if>
      <if test="deliveryed != null" >
        #{deliveryed},
      </if>
      <if test="suitId != null" >
        #{suitId},
      </if>
      <if test="times != null" >
        #{times},
      </if>
      <if test="view != null" >
        #{view},
      </if>
      <if test="isFull != null">
        #{isFull},
      </if>
      <if test="deliveryId != null">
        #{deliveryId},
      </if>
      <if test="recoveryType != null">
        #{recoveryType},
      </if>
      <if test="afterSaleRemarkType != null">
        #{afterSaleRemarkType},
      </if>
      <if test="afterSaleRemark != null">
        #{afterSaleRemark},
      </if>
      <if test="afterSaleOrderStatus != null">
        #{afterSaleOrderStatus},
      </if>
      <if test="productType != null">
        #{productType},
      </if>
      <if test="refundFreight != null">
        #{refundFreight},
      </if>
      <if test="carryingGoods != null">
        #{carryingGoods},
      </if>
      <if test="snapshot != null">
        #{snapshot},
      </if>
      <if test="autoAfterSaleFlag != null">
        #{autoAfterSaleFlag},
      </if>
    </trim>
  </insert>
    <insert id="saveBatchAfterSaleOrder">
      insert into
      after_sale_order
      (add_time,m_id,account_id,after_sale_order_no,order_no,sku,status, after_sale_unit, type,deliveryed,suit_id,times,recovery_type,`view`,product_type,snapshot)
      VALUES
      <foreach collection="list"  item="item" separator=",">
        (now(),#{item.mId},#{item.accountId},#{item.afterSaleOrderNo},#{item.orderNo},#{item.sku},#{item.status},
        #{item.afterSaleUnit},#{item.type},#{item.deliveryed},#{item.suitId},#{item.times},#{item.recoveryType},#{item.view},#{item.productType},#{item.snapshot})
      </foreach>
    </insert>


    <select id="countByMId" resultType="java.lang.Integer">
    SELECT count(1) FROM after_sale_order t WHERE t.m_id = #{mId} and t.account_id = #{accountId}
  </select>

  <select id="countView" resultType="java.lang.Integer">
    SELECT count(1) FROM after_sale_order t WHERE t.m_id = #{mId} and t.account_id = #{accountId} and view=0
  </select>

  <update id="updateStatus">
    update after_sale_order t
    SET t.status = #{status}
    WHERE t.after_sale_order_no = #{afterSaleOrderNo}
  </update>

  <select id="selectByAfterSaleOrderNo" parameterType="java.lang.String" resultType="net.summerfarm.mall.model.vo.AfterSaleOrderVO">
    select t.after_sale_order_no afterSaleOrderNo,t.add_time addTime, t.after_sale_unit afterSaleUnit, t.type,t.deliveryed,t.order_no orderNo,t.suit_id suitId,t.m_id mId,t.account_id accountId,t.sku,p.id,p.after_sale_order_no afterSaleOrderNo,p.handler,p.proof_pic proofPic,p.handle_remark handleRemark,p.quantity,p.after_sale_type afterSaleType,p.auditer,p.handle_num handleNum,p.handle_type handleType,p.status,p.apply_remark applyRemark,
    p.updatetime,p.refund_type refundType,t.status,t.delivery_id deliveryId,IFNULL(p.recovery_num,0) recoveryNum,t.refund_freight refundFreight,t.product_type productType,t.carrying_goods carryingGoods
    from after_sale_order t
           left join (select * from (select * from after_sale_proof where after_sale_order_no = #{afterSaleOrderNo}  order by  id desc )afp group by afp.after_sale_order_no) p on p.after_sale_order_no=t.after_sale_order_no
    where t.after_sale_order_no = #{afterSaleOrderNo}
  </select>


  <select id="selectDistinct" parameterType="java.lang.Long" resultMap="BaseResultMap">
    SELECT t.sku, t.order_no, t.m_id, t.account_id accountId,t.suit_id
    FROM after_sale_order t
    WHERE t.m_id = #{mId} and t.account_id = #{accountId}
    GROUP BY t.sku, t.order_no,t.suit_id
    order by t.add_time,t.order_no desc
  </select>



  <select id="selectJiSuAfterSaleOrder" resultType="net.summerfarm.mall.model.vo.AfterSaleOrderVO">
    SELECT p.status,p.handle_num handleNum,aso.view
    FROM after_sale_order aso
/*    left join (select * from (select * from after_sale_proof  order by  id desc )afp group by afp.after_sale_order_no p on p.after_sale_order_no=aso.after_sale_order_no
*/
    WHERE aso.m_id = #{mId}
    AND aso.type = 1
    <if test="beginTime != null">
      AND aso.add_time <![CDATA[>=]]> #{beginTime}
    </if>
    <if test="endTime != null">
      AND aso.add_time <![CDATA[<]]> #{endTime}
    </if>
    <if test="status != null">
      AND aso.status = #{status}
    </if>
  </select>

  <update id="update" parameterType="net.summerfarm.mall.model.domain.AfterSaleOrder">
    UPDATE after_sale_order
    <set>
      <if test="view != null">
        view = #{view},
      </if>
    </set>
    WHERE after_sale_order_no = #{afterSaleOrderNo}
  </update>


  <select id="selectTimeOutProof" resultType="java.lang.String">
    SELECT aso.after_sale_order_no afterSaleOrderNo
    FROM after_sale_order aso
    left join after_sale_proof p on p.after_sale_order_no = aso.after_sale_order_no
    WHERE  aso.status = 4 AND p.updatetime <![CDATA[<]]> NOW() - INTERVAL 24 HOUR
  </select>




  <update id="updateByAfterSaleOrderNo" parameterType="net.summerfarm.mall.model.domain.AfterSaleOrder">
    update after_sale_order
    <set>
      <if test="status != null">
        status = #{status,jdbcType=INTEGER},
      </if>
      <if test="view != null">
        view = #{view},
      </if>
      <if test="recoveryType != null">
        recovery_type = #{recoveryType},
      </if>
      <if test="refundFreight != null">
        refund_freight = #{refundFreight},
      </if>
      <if test="closer != null">
        closer = #{closer},
      </if>
      <if test="closeTime != null">
        close_time = #{closeTime},
      </if>
      <if test="afterSaleRemark != null">
        after_sale_remark = #{afterSaleRemark},
      </if>
      <if test="snapshot != null">
        snapshot = #{snapshot},
      </if>
    </set>
    where after_sale_order_no = #{afterSaleOrderNo}
  </update>


  <select id="selectSumPrice" resultType="java.math.BigDecimal">
    select sum(asp.handle_num) from after_sale_order aso
      inner join after_sale_proof asp on aso.after_sale_order_no = asp.after_sale_order_no
      where aso.type = 1 and aso.m_id = #{mId} and aso.add_time <![CDATA[>=]]> #{startTime} and aso.add_time <![CDATA[<]]> #{endTime}
  </select>

  <select id="selectByOrderNoAndSku" resultType="net.summerfarm.mall.model.vo.AfterSaleOrderVO">

    select * from after_sale_order aso
    left join after_sale_proof asp on aso.after_sale_order_no = asp.after_sale_order_no
    where aso.status in(0,1,2) and aso.order_no = #{orderNo} and aso.sku = #{sku}
  </select>

  <select id="selectSuccessAfterSale" resultType="net.summerfarm.mall.model.vo.AfterSaleOrderVO"
          parameterType="net.summerfarm.mall.model.domain.AfterSaleOrder">
    select
    distinct t.id, t.after_sale_order_no afterSaleOrderNo, t.m_id mId, t.account_id accountId, t.order_no orderNo, t.sku,t.after_sale_unit afterSaleUnit,t.view,t.suit_id suitId,t.deliveryed,
    t.add_time addTime, m.mname,a.area_name areaName,oi.pd_name pdName,oi.weight,t.type, m.area_no areaNo,t.times,msa.contact accountContact,msa.phone accountPhone,t.delivery_id deliveryId,
    concat(con.province,con.city,con.area,con.address) address,dp.quantity deliveryQuantity ,dp.delivery_time deliveryDate
    FROM after_sale_order t
    LEFT JOIN merchant m on t.m_id = m.m_id
    LEFT JOIN order_item oi on t.order_no = oi.order_no AND t.sku = oi.sku
    left join merchant_sub_account msa on t.account_id = msa.account_id
    LEFT JOIN delivery_plan dp on dp.id = t.delivery_id
    left join contact con on con.contact_id  = dp.contact_id
    LEFT JOIN area a on m.area_no =a.area_no
    <where>
      t.status = 2
      <if test="afterSaleOrderNo != null">
        AND t.after_sale_order_no like concat('%',#{afterSaleOrderNo},'%')
      </if>

      <if test="orderNo != null">
        AND t.order_no =#{orderNo}
      </if>
      <if test="type != null">
        AND t.type = #{type}
      </if>
      <if test="suitId != null">
        AND t.suit_id = #{suitId}
      </if>
      <if test="sku != null">
        AND t.sku = #{sku}
      </if>
      <if test="mId != null">
        AND m.m_id = #{mId}
      </if>
      <if test="deliveryed != null">
        AND t.deliveryed = #{deliveryed}
      </if>
    </where>
    <if test="orderNo == null">
      order by t.add_time desc,t.order_no desc
    </if>
    <if test="orderNo != null">
      order by t.add_time
    </if>
  </select>
  <select id="selectAfterSale" resultType="net.summerfarm.mall.model.vo.AfterSaleOrderVO"
          parameterType="net.summerfarm.mall.model.domain.AfterSaleOrder">
    select
    distinct t.id, t.after_sale_order_no afterSaleOrderNo, t.m_id mId, t.account_id accountId, t.order_no orderNo, t.sku,t.after_sale_unit afterSaleUnit,t.view,t.suit_id suitId,t.deliveryed,
    t.add_time addTime, m.mname,a.area_name areaName,oi.pd_name pdName,oi.weight,t.type, m.area_no areaNo,t.times,msa.contact accountContact,msa.phone accountPhone,t.delivery_id deliveryId,
    concat(con.province,con.city,con.area,con.address) address,dp.quantity deliveryQuantity ,dp.delivery_time deliveryDate
    FROM after_sale_order t
    LEFT JOIN merchant m on t.m_id = m.m_id
    LEFT JOIN order_item oi on t.order_no = oi.order_no AND t.sku = oi.sku
    left join merchant_sub_account msa on t.account_id = msa.account_id
    LEFT JOIN delivery_plan dp on dp.id = t.delivery_id
    left join contact con on con.contact_id  = dp.contact_id
    LEFT JOIN area a on m.area_no =a.area_no
    <where>
      t.status in (0,1,2)
      <if test="afterSaleOrderNo != null">
        AND t.after_sale_order_no like concat('%',#{afterSaleOrderNo},'%')
      </if>

      <if test="orderNo != null">
        AND t.order_no =#{orderNo}
      </if>
      <if test="type != null">
        AND t.type = #{type}
      </if>
      <if test="suitId != null">
        AND t.suit_id = #{suitId}
      </if>
      <if test="sku != null">
        AND t.sku = #{sku}
      </if>
      <if test="mId != null">
        AND m.m_id = #{mId}
      </if>
      <if test="deliveryed != null">
        AND t.deliveryed = #{deliveryed}
      </if>
      <if test="deliveryId != null">
        AND t.delivery_id = #{deliveryId}
      </if>
    </where>
    <if test="orderNo == null">
      order by t.add_time desc,t.order_no desc
    </if>
    <if test="orderNo != null">
      order by t.add_time
    </if>
  </select>

  <select id="selectNew" resultType="net.summerfarm.mall.model.vo.AfterSaleOrderVO"
          parameterType="net.summerfarm.mall.model.domain.AfterSaleOrder">
    select
    distinct t.id, t.after_sale_order_no afterSaleOrderNo, t.m_id mId, t.account_id accountId, t.order_no orderNo, t.sku, t.after_sale_unit afterSaleUnit,t.view,t.suit_id suitId,t.deliveryed,
    t.add_time addTime, m.mname,a.area_name areaName,oi.pd_name pdName,oi.weight,t.type, m.area_no areaNo,t.times,msa.contact accountContact,msa.phone accountPhone,t.delivery_id deliveryId,t.product_type productType,
    concat(con.province,con.city,con.area,con.address) address,dp.quantity deliveryQuantity ,dp.delivery_time deliveryDate,t.status
    FROM after_sale_order t
    LEFT JOIN merchant m on t.m_id = m.m_id
    LEFT JOIN order_item oi on t.order_no = oi.order_no AND t.sku = oi.sku and oi.product_type = t.product_type
    left join merchant_sub_account msa on t.account_id = msa.account_id
    LEFT JOIN delivery_plan dp on dp.id = t.delivery_id
    left join contact con on con.contact_id  = dp.contact_id
    LEFT JOIN area a on m.area_no =a.area_no
    <where>
      <if test="afterSaleOrderNo != null">
        AND t.after_sale_order_no like concat('%',#{afterSaleOrderNo},'%')
      </if>
      <if test="status != null">
        AND t.status = #{status}
      </if>
      <if test="orderNo != null">
        AND t.order_no =#{orderNo}
      </if>
      <if test="type != null">
        AND t.type = #{type}
      </if>
      <if test="suitId != null">
        AND t.suit_id = #{suitId}
      </if>
      <if test="sku != null">
        AND t.sku = #{sku}
      </if>
      <if test="mId != null">
        AND m.m_id = #{mId}
      </if>
      <if test="deliveryed != null">
        AND t.deliveryed = #{deliveryed}
      </if>
      <if test="productType != null">
        AND t.product_type = #{productType}
      </if>
    </where>
    <if test="orderNo == null">
      order by t.add_time desc,t.order_no desc, t.after_sale_order_no
    </if>
    <if test="orderNo != null">
      order by t.add_time, t.after_sale_order_no
    </if>
  </select>

    <select id="selectSpeedAfterSaleOrder" resultType="net.summerfarm.mall.model.vo.AfterSaleOrderVO">
      SELECT aso.after_sale_order_no afterSaleOrderNo, aso.view
      FROM after_sale_order aso
      WHERE aso.m_id = #{mId}
      AND aso.type = 1
      <if test="beginTime != null">
        AND aso.add_time <![CDATA[>=]]> #{beginTime}
      </if>
      <if test="endTime != null">
        AND aso.add_time <![CDATA[<]]> #{endTime}
      </if>
      <if test="status != null">
        AND aso.status = #{status}
      </if>
    </select>
  <select id="selectAfterOrder" resultType="net.summerfarm.mall.model.domain.AfterSaleOrder">
     SELECT t.sku, t.order_no, t.m_id, t.account_id accountId,t.suit_id suitId,t.status status,t.after_sale_order_no afterSaleOrderNo
    FROM after_sale_order t
    where t.order_no = #{orderNo}
  </select>

  <select id="selectAfterOrderDeposit" resultType="net.summerfarm.mall.model.domain.AfterSaleOrder">
     SELECT t.sku, t.order_no orderNo, t.m_id mId, t.account_id accountId,t.suit_id suitId,t.after_sale_order_no afterSaleOrderNo
    FROM after_sale_order t
    where t.status = 12
  </select>
  <select id="selectNotStockSuccessQuantity" resultType="java.lang.Integer">
    SELECT IFNULL(SUM(asp.quantity),0) FROM after_sale_proof asp WHERE after_sale_order_no in
    (SELECT after_sale_order_no FROM after_sale_order WHERE order_no = #{orderNo} and deliveryed = 0) and status = 2
  </select>

  <select id="selectSuccessQuantity" resultType="java.lang.Integer">
    SELECT IFNULL(SUM(asp.quantity),0) FROM after_sale_proof asp WHERE after_sale_order_no in
    (SELECT after_sale_order_no FROM after_sale_order WHERE order_no = #{orderNo} ) and status = 2
    and handle_type not in (4,6,7)
  </select>

  <select id="selectAllSuccessQuantity" resultType="java.lang.Integer">
    SELECT IFNULL(SUM(asp.quantity),0)  FROM after_sale_proof asp WHERE after_sale_order_no in
    (SELECT after_sale_order_no FROM after_sale_order WHERE order_no = #{orderNo} and deliveryed = 0) and status = 2
  </select>

  <select id="selectAfterQuantityByOrderNo" resultMap="OrderProofMap">
  select aso.after_sale_unit,asp.quantity,asp.handle_type,asp.handle_num,aso.deliveryed,IFNULL(recovery_num,0) recovery_num,aso.sku, aso.after_sale_order_no
  from after_sale_order aso
  left join after_sale_proof asp ON aso.after_sale_order_no = asp.after_sale_order_no
  where asp.status in (0,1,2) and asp.handle_type not in (6,7)
    <if test="orderNo != null">
      and aso.order_no = #{orderNo}
    </if>
    <if test="sku != null">
      and aso.sku = #{sku}
    </if>
    <if test="deliveryPlanId != null">
      and aso.delivery_id = #{deliveryPlanId}
    </if>
    <if test="suitId != null">
      and aso.suit_id = #{suitId}
    </if>
    <if test="afterSaleOrderNo != null">
      and aso.after_sale_order_no != #{afterSaleOrderNo}
    </if>
    <if test="productType != null">
      and aso.product_type = #{productType}
    </if>
  </select>
  <select id="selectByOrderNoAndSuitId" resultMap="OrderProofMap">
    SELECT aso.sku, aso.order_no, aso.m_id, aso.account_id ,aso.suit_id ,aso.status status,aso.after_sale_order_no ,
    aso.after_sale_unit,asp.quantity,asp.handle_type,aso.deliveryed
    FROM after_sale_order aso
    left join after_sale_proof asp ON aso.after_sale_order_no = asp.after_sale_order_no
    where aso.order_no = #{orderNo} and aso.suit_id = #{suitId} and asp.status in (0,1,2) and asp.handle_type not in (6,7)
    <if test="afterSaleOrderNo != null">
      and aso.after_sale_order_no != #{afterSaleOrderNo}
    </if>
  </select>
  <select id="selectAfterByHandle" resultMap="OrderProofMap">
    select aso.after_sale_unit,asp.quantity,asp.handle_type,asp.handle_num,aso.deliveryed
    from after_sale_order aso
    left join after_sale_proof asp ON aso.after_sale_order_no = asp.after_sale_order_no
    where asp.status in (0,1,2) and asp.handle_type in (5,4)
    <if test="orderNo != null">
      and aso.order_no = #{orderNo}
    </if>
    <if test="sku != null">
      and aso.sku = #{sku}
    </if>
    <if test="deliveryPlanId != null">
      and aso.delivery_id = #{deliveryPlanId}
    </if>
    <if test="suitId != null">
      and aso.suit_id = #{suitId}
    </if>
    <if test="productType != null">
      and aso.product_type = #{productType}
    </if>
  </select>
  <select id="countBydeliveryPlanId" resultType="java.lang.Integer">
    select count(1) from after_sale_order where delivery_id = #{dpId} and status not in (3,11)
  </select>
  <select id="selectNotAfter" resultMap="OrderProofMap">
    select aso.after_sale_order_no,aso.after_sale_unit,asp.quantity,asp.handle_type,asp.handle_num,aso.deliveryed,aso.sku,aso.suit_id
    from after_sale_order aso
    left join after_sale_proof asp ON aso.after_sale_order_no = asp.after_sale_order_no
    where aso.deliveryed = 0 and asp.status = 2 and order_no=#{orderNo}
  </select>
  <select id="selectHBAfterByOrderNo" resultType="java.lang.Integer">
    select count(1)  from after_sale_order aso
    left join after_sale_proof asp ON aso.after_sale_order_no = asp.after_sale_order_no
    where asp.status in (0,1,2) and asp.handle_type in (6,7)
    and order_no = #{order_no}
  </select>
  <select id="selectByDeliveryFeeCount" resultType="java.lang.Integer">
    select count(1) from after_sale_order
    where order_no=#{orderNo} and refund_freight = 1 and status in (0,1,2)
    <if test="afterSaleOrderNo != null">
      and after_sale_order_no != #{afterSaleOrderNo}
    </if>
  </select>
  <select id="selectNotQuantityByOrderNo" resultType="java.lang.Integer">
    select SUM(asp.quantity)
    from after_sale_order aso
    left join after_sale_proof asp ON aso.after_sale_order_no = asp.after_sale_order_no
    where asp.status in (0,1,2) and asp.handle_type not in (6,7) and aso.deliveryed = 0
    <if test="orderNo != null">
      and aso.order_no = #{orderNo}
    </if>
  </select>

  <select id="selectSuccessByAfterSaleOrder" resultMap="OrderProofMap"
          parameterType="net.summerfarm.mall.model.domain.AfterSaleOrder">
    select aso.after_sale_unit,asp.quantity,asp.handle_type,asp.handle_num,aso.deliveryed,IFNULL(recovery_num,0) recovery_num,aso.sku
    from after_sale_order aso
    left join after_sale_proof asp ON aso.after_sale_order_no = asp.after_sale_order_no
    where asp.status = 2 and asp.handle_type not in (6,7)
    and aso.deliveryed = 0
    <if test="orderNo != null">
      and aso.order_no = #{orderNo}
    </if>
    <if test="sku != null">
      and aso.sku = #{sku}
    </if>
    <if test="afterSaleOrderNo != null">
      and aso.after_sale_order_no != #{afterSaleOrderNo}
    </if>
    <if test="suitId != null">
      and aso.suit_id = #{suitId}
    </if>
    <if test="productType != null">
        and product_type = #{productType}
    </if>
  </select>
    <select id="selectExchangeByOrderNo" resultType="java.lang.Integer">
      select COUNT(1) from after_sale_order aso
    left join after_sale_proof asp ON aso.after_sale_order_no = asp.after_sale_order_no
    where asp.status = 2 and asp.handle_type in (6,7)
    and aso.order_no = #{orderNo}
    </select>

  <select id="selectVoByAfterSaleOrderNo" parameterType="java.lang.String" resultType="net.summerfarm.mall.model.vo.AfterSaleOrderVO">
    select t.after_sale_order_no afterSaleOrderNo,t.add_time addTime, t.after_sale_unit afterSaleUnit, t.type,t.deliveryed,t.order_no orderNo,t.suit_id suitId,t.m_id mId,t.account_id accountId,t.sku,
    p.id,p.after_sale_order_no afterSaleOrderNo,p.handler,p.proof_pic proofPic,p.handle_remark handleRemark,p.quantity,p.after_sale_type afterSaleType,p.auditer,p.handle_num handleNum,p.handle_type handleType,p.status,p.apply_remark applyRemark,p.applyer,
    p.handletime handleTime, p.updatetime,p.refund_type refundType,t.status,t.delivery_id deliveryId,IFNULL(p.recovery_num,0) recoveryNum,t.refund_freight refundFreight,t.product_type productType,t.carrying_goods carryingGoods
    from after_sale_order t
    left join after_sale_proof p
    on p.after_sale_order_no=t.after_sale_order_no
    where t.after_sale_order_no = #{afterSaleOrderNo}
  </select>
  <select id="selectAfterOrderReissue" resultType="net.summerfarm.mall.model.domain.AfterSaleOrder">
     select order_no orderNo
      from after_sale_order
      where order_no = #{orderNo} and suit_id =#{suitId} and sku = #{sku} and status in(2,3,6)
  </select>
  <select id="afterSaleCountByMId" resultType="java.lang.Integer">
    SELECT count(1) FROM after_sale_order t WHERE t.m_id = #{mId} and t.account_id = #{accountId}
  </select>
  <select id="countByAfterSaleOrderNo" resultType="java.lang.Integer">
    SELECT count(1) FROM after_sale_order t WHERE t.after_sale_order_no = #{afterSaleOrderNo}
  </select>

  <select id="getUnfilledAfterSaleOrderByMid" resultType="java.lang.Integer" parameterType="java.lang.Long">
    SELECT count(1) FROM after_sale_order WHERE m_id = #{mId} and status in(0, 1)
    </select>

  <select id="selectRefundSuccessInfo" resultType="net.summerfarm.mall.model.vo.AfterSaleOrderVO"
          parameterType="net.summerfarm.mall.model.domain.AfterSaleOrder">
    SELECT asf.handle_num handleNum, aso.after_sale_order_no afterSaleOrderNo, aso.sku FROM after_sale_order aso
    LEFT JOIN after_sale_proof asf on aso.after_sale_order_no = asf.after_sale_order_no
    WHERE asf.`status` = 2 and asf.handle_type in (2, 4, 9, 11)
      <if test="orderNo != null">
        and aso.order_no = #{orderNo}
      </if>
      <if test="sku != null">
        and aso.sku = #{sku}
      </if>
      <if test="mId != null">
        and aso.m_id = #{mId}
      </if>
      <if test="deliveryId != null">
        and aso.delivery_id = #{deliveryId}
      </if>
  </select>

  <select id="selectByOrderNoNew" parameterType="net.summerfarm.mall.model.domain.AfterSaleOrder" resultType="net.summerfarm.mall.model.vo.AfterSaleOrderVO">
    select t.after_sale_order_no afterSaleOrderNo,t.add_time addTime, t.after_sale_unit afterSaleUnit, t.order_no orderNo,
    t.type ,t.status,t.sku,t.suit_id suitId,t.deliveryed ,t.delivery_id deliveryId, p.pd_name pdName, i.weight
    FROM after_sale_order t
    left join inventory i on i.sku = t.sku
    left join products p on i.pd_id = p.pd_id
    WHERE t.order_no = #{orderNo} and t.status in(0,1,2)
    <if test="sku != null">
      AND t.sku = #{sku}
    </if>
    <if test="deliveryId != null">
      AND t.delivery_id = #{deliveryId}
    </if>
  </select>

  <select id="queryByOrderNoQuantity" resultType="java.lang.Integer">
    select sum(asp.quantity) from after_sale_order aso
                                    inner join after_sale_proof asp on asp.after_sale_order_no = aso.after_sale_order_no
    where aso.order_no = #{orderNo} and  aso.sku =#{sku} and aso.add_time > #{afterTime}
      and aso.deliveryed = 0 and asp.refund_type !='其他'and aso.status = 2
  </select>

  <select id="queryAfterOrderQuantity" resultType="java.lang.Integer">
    select sum(asp.quantity) from after_sale_order aso
                                    inner join after_sale_proof asp on aso.after_sale_order_no = asp.after_sale_order_no
                                    inner join delivery_plan dp on dp.order_no = aso.order_no and dp.delivery_time = #{deliveryDate} and dp.deliverytype = 0
    where aso.status = 2 and aso.deliveryed = 0 and  aso.add_time > #{addTime} and aso.sku = #{sku} and asp.refund_type != '其他'
  </select>

  <select id="selectAfterSaleReissueOrder" resultType="net.summerfarm.mall.model.vo.AfterSaleOrderVO">
    select t.after_sale_order_no afterSaleOrderNo,t.m_id mId,t.add_time addTime, t.after_sale_unit afterSaleUnit, t.order_no orderNo,
    t.type ,t.status,t.sku,t.suit_id suitId,t.deliveryed ,t.delivery_id deliveryId,asf.applyer
    from after_sale_order t LEFT JOIN after_sale_proof asf on t.after_sale_order_no = asf.after_sale_order_no
    where asf.handle_type = 7 and t.m_id = #{mId} and t.sku = #{sku} and t.add_time >= #{startTime} and t.add_time <![CDATA[<]]> #{endTime}
  </select>
    <select id="queryByAfterSaleOrderNo" resultType="net.summerfarm.mall.model.vo.AfterSaleOrderVO">
        select t.m_id mId, t.account_id accountId, t.after_sale_order_no afterSaleOrderNo,t.add_time addTime, t.after_sale_unit afterSaleUnit, t.type,t.order_no orderNo,t.deliveryed,t.suit_id suitId,t.sku
        ,t.delivery_id deliveryId,t.status,i.sub_type subType
        from after_sale_order t LEFT JOIN inventory i on t.sku = i.sku
        where t.after_sale_order_no = #{afterSaleOrderNo}
    </select>

  <select id="selectAfterSaleOrderByOrderNo" resultType="net.summerfarm.mall.model.vo.AfterSaleOrderVO">
    select aso.after_sale_unit afterSaleUnit,asp.quantity,asp.handle_type handleType,asp.handle_num handleNum,aso.deliveryed,aso.sku,aso.status
    from after_sale_order aso
           left join after_sale_proof asp ON aso.after_sale_order_no = asp.after_sale_order_no
    where  aso.order_no = #{orderNo}  and  aso.status not in (3,11)
  </select>

  <select id="selectAutoAfterSaleByOrderNoAndSku" resultMap="BaseResultMap">
    SELECT after_sale_order_no FROM after_sale_order WHERE order_no = #{orderNo} AND sku = #{sku} AND auto_after_sale_flag = 1
  </select>
</mapper>