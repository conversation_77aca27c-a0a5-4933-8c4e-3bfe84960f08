<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.mall.mapper.MasterPaymentMapper">

    <resultMap id="BaseResultMap" type="net.summerfarm.mall.model.domain.MasterPayment">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="payType" column="pay_type" jdbcType="VARCHAR"/>
            <result property="masterOrderNo" column="master_order_no" jdbcType="VARCHAR"/>
            <result property="transactionNumber" column="transaction_number" jdbcType="VARCHAR"/>
            <result property="money" column="money" jdbcType="DECIMAL"/>
            <result property="endTime" column="end_time" jdbcType="TIMESTAMP"/>
            <result property="tradeType" column="trade_type" jdbcType="VARCHAR"/>
            <result property="bankType" column="bank_type" jdbcType="VARCHAR"/>
            <result property="status" column="status" jdbcType="TINYINT"/>
            <result property="errCode" column="err_code" jdbcType="VARCHAR"/>
            <result property="errCodeDes" column="err_code_des" jdbcType="VARCHAR"/>
            <result property="companyAccountId" column="company_account_id" jdbcType="INTEGER"/>
            <result property="scanCode" column="scan_code" jdbcType="VARCHAR"/>
            <result property="accountInfo" column="account_info" jdbcType="VARCHAR"/>
            <result property="bocPayType" column="boc_pay_type" jdbcType="VARCHAR"/>
            <result property="onlinePayEndTime" column="online_pay_end_time" jdbcType="TIMESTAMP"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,pay_type,master_order_no,
        transaction_number,money,end_time,
        trade_type,bank_type,status,
        err_code,err_code_des,company_account_id,
        scan_code,account_info,boc_pay_type,
        online_pay_end_time,create_time,update_time
    </sql>

    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from master_payment
        where  id = #{id,jdbcType=BIGINT}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete from master_payment
        where  id = #{id,jdbcType=BIGINT}
    </delete>
    <insert id="insert" keyColumn="id" keyProperty="id" parameterType="net.summerfarm.mall.model.domain.MasterPayment" useGeneratedKeys="true">
        insert into master_payment
        ( id,pay_type,master_order_no
        ,transaction_number,money,end_time
        ,trade_type,bank_type,status
        ,err_code,err_code_des,company_account_id
        ,scan_code,account_info,boc_pay_type
        ,online_pay_end_time,create_time,update_time
        )
        values (#{id,jdbcType=BIGINT},#{payType,jdbcType=VARCHAR},#{masterOrderNo,jdbcType=VARCHAR}
        ,#{transactionNumber,jdbcType=VARCHAR},#{money,jdbcType=DECIMAL},#{endTime,jdbcType=TIMESTAMP}
        ,#{tradeType,jdbcType=VARCHAR},#{bankType,jdbcType=VARCHAR},#{status,jdbcType=TINYINT}
        ,#{errCode,jdbcType=VARCHAR},#{errCodeDes,jdbcType=VARCHAR},#{companyAccountId,jdbcType=INTEGER}
        ,#{scanCode,jdbcType=VARCHAR},#{accountInfo,jdbcType=VARCHAR},#{bocPayType,jdbcType=VARCHAR}
        ,#{onlinePayEndTime,jdbcType=TIMESTAMP},#{createTime,jdbcType=TIMESTAMP},#{updateTime,jdbcType=TIMESTAMP}
        )
    </insert>
    <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="net.summerfarm.mall.model.domain.MasterPayment" useGeneratedKeys="true">
        insert into master_payment
        <trim prefix="(" suffix=")" suffixOverrides=",">
                <if test="id != null">id,</if>
                <if test="payType != null">pay_type,</if>
                <if test="masterOrderNo != null">master_order_no,</if>
                <if test="transactionNumber != null">transaction_number,</if>
                <if test="money != null">money,</if>
                <if test="endTime != null">end_time,</if>
                <if test="tradeType != null">trade_type,</if>
                <if test="bankType != null">bank_type,</if>
                <if test="status != null">status,</if>
                <if test="errCode != null">err_code,</if>
                <if test="errCodeDes != null">err_code_des,</if>
                <if test="companyAccountId != null">company_account_id,</if>
                <if test="scanCode != null">scan_code,</if>
                <if test="accountInfo != null">account_info,</if>
                <if test="bocPayType != null">boc_pay_type,</if>
                <if test="onlinePayEndTime != null">online_pay_end_time,</if>
                <if test="createTime != null">create_time,</if>
                <if test="updateTime != null">update_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
                <if test="id != null">#{id,jdbcType=BIGINT},</if>
                <if test="payType != null">#{payType,jdbcType=VARCHAR},</if>
                <if test="masterOrderNo != null">#{masterOrderNo,jdbcType=VARCHAR},</if>
                <if test="transactionNumber != null">#{transactionNumber,jdbcType=VARCHAR},</if>
                <if test="money != null">#{money,jdbcType=DECIMAL},</if>
                <if test="endTime != null">#{endTime,jdbcType=TIMESTAMP},</if>
                <if test="tradeType != null">#{tradeType,jdbcType=VARCHAR},</if>
                <if test="bankType != null">#{bankType,jdbcType=VARCHAR},</if>
                <if test="status != null">#{status,jdbcType=TINYINT},</if>
                <if test="errCode != null">#{errCode,jdbcType=VARCHAR},</if>
                <if test="errCodeDes != null">#{errCodeDes,jdbcType=VARCHAR},</if>
                <if test="companyAccountId != null">#{companyAccountId,jdbcType=INTEGER},</if>
                <if test="scanCode != null">#{scanCode,jdbcType=VARCHAR},</if>
                <if test="accountInfo != null">#{accountInfo,jdbcType=VARCHAR},</if>
                <if test="bocPayType != null">#{bocPayType,jdbcType=VARCHAR},</if>
                <if test="onlinePayEndTime != null">#{onlinePayEndTime,jdbcType=TIMESTAMP},</if>
                <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
                <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="net.summerfarm.mall.model.domain.MasterPayment">
        update master_payment
        <set>
                <if test="payType != null">
                    pay_type = #{payType,jdbcType=VARCHAR},
                </if>
                <if test="masterOrderNo != null">
                    master_order_no = #{masterOrderNo,jdbcType=VARCHAR},
                </if>
                <if test="transactionNumber != null">
                    transaction_number = #{transactionNumber,jdbcType=VARCHAR},
                </if>
                <if test="money != null">
                    money = #{money,jdbcType=DECIMAL},
                </if>
                <if test="endTime != null">
                    end_time = #{endTime,jdbcType=TIMESTAMP},
                </if>
                <if test="tradeType != null">
                    trade_type = #{tradeType,jdbcType=VARCHAR},
                </if>
                <if test="bankType != null">
                    bank_type = #{bankType,jdbcType=VARCHAR},
                </if>
                <if test="status != null">
                    status = #{status,jdbcType=TINYINT},
                </if>
                <if test="errCode != null">
                    err_code = #{errCode,jdbcType=VARCHAR},
                </if>
                <if test="errCodeDes != null">
                    err_code_des = #{errCodeDes,jdbcType=VARCHAR},
                </if>
                <if test="companyAccountId != null">
                    company_account_id = #{companyAccountId,jdbcType=INTEGER},
                </if>
                <if test="scanCode != null">
                    scan_code = #{scanCode,jdbcType=VARCHAR},
                </if>
                <if test="accountInfo != null">
                    account_info = #{accountInfo,jdbcType=VARCHAR},
                </if>
                <if test="bocPayType != null">
                    boc_pay_type = #{bocPayType,jdbcType=VARCHAR},
                </if>
                <if test="onlinePayEndTime != null">
                    online_pay_end_time = #{onlinePayEndTime,jdbcType=TIMESTAMP},
                </if>
                <if test="createTime != null">
                    create_time = #{createTime,jdbcType=TIMESTAMP},
                </if>
                <if test="updateTime != null">
                    update_time = #{updateTime,jdbcType=TIMESTAMP},
                </if>
        </set>
        where   id = #{id,jdbcType=BIGINT}
    </update>
    <update id="updateByPrimaryKey" parameterType="net.summerfarm.mall.model.domain.MasterPayment">
        update master_payment
        set
            pay_type =  #{payType,jdbcType=VARCHAR},
            master_order_no =  #{masterOrderNo,jdbcType=VARCHAR},
            transaction_number =  #{transactionNumber,jdbcType=VARCHAR},
            money =  #{money,jdbcType=DECIMAL},
            end_time =  #{endTime,jdbcType=TIMESTAMP},
            trade_type =  #{tradeType,jdbcType=VARCHAR},
            bank_type =  #{bankType,jdbcType=VARCHAR},
            status =  #{status,jdbcType=TINYINT},
            err_code =  #{errCode,jdbcType=VARCHAR},
            err_code_des =  #{errCodeDes,jdbcType=VARCHAR},
            company_account_id =  #{companyAccountId,jdbcType=INTEGER},
            scan_code =  #{scanCode,jdbcType=VARCHAR},
            account_info =  #{accountInfo,jdbcType=VARCHAR},
            boc_pay_type =  #{bocPayType,jdbcType=VARCHAR},
            online_pay_end_time =  #{onlinePayEndTime,jdbcType=TIMESTAMP},
            create_time =  #{createTime,jdbcType=TIMESTAMP},
            update_time =  #{updateTime,jdbcType=TIMESTAMP}
        where   id = #{id,jdbcType=BIGINT}
    </update>
    <select id="selectByMasterPaymentNo" parameterType="java.lang.String" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from master_payment
        where master_order_no =  #{masterPaymentNo,jdbcType=VARCHAR}
        order by id desc
        limit 1
    </select>
    <update id="updateByMasterPaymentNoSelective" parameterType="net.summerfarm.mall.model.domain.MasterPayment">
        update master_payment
        <set>
          <if test="payType != null">
            pay_type = #{payType,jdbcType=VARCHAR},
          </if>
          <if test="transactionNumber != null">
            transaction_number = #{transactionNumber,jdbcType=VARCHAR},
          </if>
          <if test="money != null">
            money = #{money,jdbcType=DECIMAL},
          </if>
          <if test="endTime != null">
            end_time = #{endTime,jdbcType=TIMESTAMP},
          </if>
          <if test="tradeType != null">
            trade_type = #{tradeType,jdbcType=VARCHAR},
          </if>
          <if test="bankType != null">
            bank_type = #{bankType,jdbcType=VARCHAR},
          </if>
          <if test="status != null">
            status = #{status,jdbcType=TINYINT},
          </if>
          <if test="errCode != null">
            err_code = #{errCode,jdbcType=VARCHAR},
          </if>
          <if test="errCodeDes != null">
            err_code_des = #{errCodeDes,jdbcType=VARCHAR},
          </if>
          <if test="companyAccountId != null">
            company_account_id = #{companyAccountId,jdbcType=INTEGER},
          </if>
          <if test="scanCode != null">
            scan_code = #{scanCode,jdbcType=VARCHAR},
          </if>
          <if test="accountInfo != null">
            account_info = #{accountInfo,jdbcType=VARCHAR},
          </if>
          <if test="bocPayType != null">
            boc_pay_type = #{bocPayType,jdbcType=VARCHAR},
          </if>
          <if test="onlinePayEndTime != null">
            online_pay_end_time = #{onlinePayEndTime,jdbcType=TIMESTAMP},
          </if>
          <if test="createTime != null">
            create_time = #{createTime,jdbcType=TIMESTAMP},
          </if>
          <if test="updateTime != null">
            update_time = #{updateTime,jdbcType=TIMESTAMP},
          </if>
        </set>
        where  master_order_no = #{masterOrderNo,jdbcType=BIGINT}
        order by id desc
        limit 1
    </update>

    <select id="selectByStatusAndTimeRange" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from master_payment
        where status = #{status}
        <if test="startTime != null">
            and create_time >= #{startTime}
        </if>
        <if test="endTime != null">
            and create_time &lt;= #{endTime}
        </if>
        order by id
        <if test="limit != null">
            limit #{limit}
        </if>
    </select>

    <select id="selectByMasterOrderNos" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from master_payment
        where master_order_no in
        <foreach collection="masterOrderNos" item="masterOrderNo" open="(" separator="," close=")">
            #{masterOrderNo}
        </foreach>
        order by id desc
    </select>
</mapper>
