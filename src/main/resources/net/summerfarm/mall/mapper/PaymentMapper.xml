<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="net.summerfarm.mall.mapper.PaymentMapper" >
  <resultMap id="BaseResultMap" type="net.summerfarm.mall.model.domain.Payment" >
    <id column="payment_id" property="paymentId" jdbcType="BIGINT" />
    <result column="pay_type" property="payType" jdbcType="VARCHAR" />
    <result column="order_no" property="orderNo" jdbcType="VARCHAR" />
    <result column="transaction_number" property="transactionNumber" jdbcType="VARCHAR" />
    <result column="money" property="money" jdbcType="DECIMAL" />
    <result column="end_time" property="endTime" jdbcType="TIMESTAMP" />
    <result column="trade_type" property="tradeType" jdbcType="VARCHAR" />
    <result column="bank_type" property="bankType" jdbcType="VARCHAR" />
    <result column="status" property="status" jdbcType="BIT" />
    <result column="err_code" property="errCode" jdbcType="VARCHAR" />
    <result column="err_code_des" property="errCodeDes" jdbcType="VARCHAR" />
    <result column="company_account_id" property="companyAccountId" jdbcType="INTEGER"/>
    <result column="scan_code" property="scanCode" jdbcType="VARCHAR"/>
    <result column="online_pay_end_time" property="onlinePayEndTime" jdbcType="TIMESTAMP"/>
    <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
    <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
  </resultMap>
  <sql id="Base_Column_List" >
    payment_id, pay_type, order_no, transaction_number, money, end_time, trade_type,
    bank_type, status, err_code, err_code_des,company_account_id, scan_code,online_pay_end_time, create_time,update_time
  </sql>

  <select id="countSuccessByOrderNo" parameterType="java.lang.String" resultType="java.lang.Integer">
    SELECT COUNT(1) AS ct FROM payment WHERE order_no= #{orderno} and status = 1
  </select>
<select id="selectRefund" parameterType="java.lang.String" resultType="hashmap">
  SELECT r.refund_no refundNo, p.money FROM payment p
  INNER JOIN refund r ON p.order_no = r.order_no
  WHERE p.order_no = #{orderNo}
</select>

  <select id="selectOne" resultMap="BaseResultMap" parameterType="net.summerfarm.mall.model.domain.Payment" >
    select
    <include refid="Base_Column_List" />
    from payment
    <where>
      <if test="orderNo != null">
        and order_no = #{orderNo}
      </if>
      <if test="status !=null">
        and status = #{status}
      </if>
      <if test="payType != null and payType != ''">
        and pay_type = #{payType}
      </if>
    </where>
    order by payment_id desc
    limit 1
  </select>

  <delete id="deleteByOrderNo" parameterType="java.lang.String" >
    delete from payment
    where order_no = #{orderNo}
    and  status = 0
  </delete>

  <insert id="insertSelective" parameterType="net.summerfarm.mall.model.domain.Payment" >
    insert into payment
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="paymentId != null" >
        payment_id,
      </if>
      <if test="payType != null" >
        pay_type,
      </if>
      <if test="orderNo != null" >
        order_no,
      </if>
      <if test="transactionNumber != null" >
        transaction_number,
      </if>
      <if test="money != null" >
        money,
      </if>
      <if test="endTime != null" >
        end_time,
      </if>
      <if test="tradeType != null" >
        trade_type,
      </if>
      <if test="bankType != null" >
        bank_type,
      </if>
      <if test="status != null" >
        status,
      </if>
      <if test="errCode != null" >
        err_code,
      </if>
      <if test="errCodeDes != null" >
        err_code_des,
      </if>
      <if test="companyAccountId != null">
        company_account_id,
      </if>
      <if test="scanCode != null">
        scan_code,
      </if>
      <if test="bocPayType != null">
        boc_pay_type,
      </if>
      <if test="onlinePayEndTime != null">
        online_pay_end_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="paymentId != null" >
        #{paymentId,jdbcType=BIGINT},
      </if>
      <if test="payType != null" >
        #{payType,jdbcType=VARCHAR},
      </if>
      <if test="orderNo != null" >
        #{orderNo,jdbcType=VARCHAR},
      </if>
      <if test="transactionNumber != null" >
        #{transactionNumber,jdbcType=VARCHAR},
      </if>
      <if test="money != null" >
        #{money,jdbcType=DECIMAL},
      </if>
      <if test="endTime != null" >
        #{endTime,jdbcType=TIMESTAMP},
      </if>
      <if test="tradeType != null" >
        #{tradeType,jdbcType=VARCHAR},
      </if>
      <if test="bankType != null" >
        #{bankType,jdbcType=VARCHAR},
      </if>
      <if test="status != null" >
        #{status,jdbcType=BIT},
      </if>
      <if test="errCode != null" >
        #{errCode,jdbcType=VARCHAR},
      </if>
      <if test="errCodeDes != null" >
        #{errCodeDes,jdbcType=VARCHAR},
      </if>
      <if test="companyAccountId != null">
        #{companyAccountId},
      </if>
      <if test="scanCode != null">
        #{scanCode},
      </if>
      <if test="bocPayType != null">
        #{bocPayType},
      </if>
      <if test="onlinePayEndTime != null">
        #{onlinePayEndTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="net.summerfarm.mall.model.domain.Payment" >
    update payment
    <set >
      <if test="payType != null" >
        pay_type = #{payType,jdbcType=VARCHAR},
      </if>
      <if test="onlinePayEndTime != null" >
        online_pay_end_time = #{onlinePayEndTime,jdbcType=TIMESTAMP},
      </if>
      <if test="orderNo != null" >
        order_no = #{orderNo,jdbcType=VARCHAR},
      </if>
      <if test="transactionNumber != null" >
        transaction_number = #{transactionNumber,jdbcType=VARCHAR},
      </if>
      <if test="money != null" >
        money = #{money,jdbcType=DECIMAL},
      </if>
      <if test="endTime != null" >
        end_time = #{endTime,jdbcType=TIMESTAMP},
      </if>
      <if test="tradeType != null" >
        trade_type = #{tradeType,jdbcType=VARCHAR},
      </if>
      <if test="bankType != null" >
        bank_type = #{bankType,jdbcType=VARCHAR},
      </if>
      <if test="status != null" >
        status = #{status,jdbcType=BIT},
      </if>
      <if test="errCode != null" >
        err_code = #{errCode,jdbcType=VARCHAR},
      </if>
      <if test="errCodeDes != null" >
        err_code_des = #{errCodeDes,jdbcType=VARCHAR},
      </if>
      <if test="companyAccountId != null">
        company_account_id = #{companyAccountId},
      </if>
    </set>
    where payment_id = #{paymentId,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="net.summerfarm.mall.model.domain.Payment" >
    update payment
    set pay_type = #{payType,jdbcType=VARCHAR},
      order_no = #{orderNo,jdbcType=VARCHAR},
      transaction_number = #{transactionNumber,jdbcType=VARCHAR},
      money = #{money,jdbcType=DECIMAL},
      end_time = #{endTime,jdbcType=TIMESTAMP},
      trade_type = #{tradeType,jdbcType=VARCHAR},
      bank_type = #{bankType,jdbcType=VARCHAR},
      status = #{status,jdbcType=BIT},
      err_code = #{errCode,jdbcType=VARCHAR},
      err_code_des = #{errCodeDes,jdbcType=VARCHAR}
    where payment_id = #{paymentId,jdbcType=BIGINT}
  </update>
  <update id="updateStatusById">
    update payment
    set status = #{status}
    where payment_id = #{id}
  </update>
  <update id="updateByOrderNo">
    update payment
    <set >
      <if test="payType != null" >
        pay_type = #{payType,jdbcType=VARCHAR},
      </if>
      <if test="onlinePayEndTime != null" >
        online_pay_end_time = #{onlinePayEndTime,jdbcType=TIMESTAMP},
      </if>
      <if test="transactionNumber != null" >
        transaction_number = #{transactionNumber,jdbcType=VARCHAR},
      </if>
      <if test="money != null" >
        money = #{money,jdbcType=DECIMAL},
      </if>
      <if test="endTime != null" >
        end_time = #{endTime,jdbcType=TIMESTAMP},
      </if>
      <if test="tradeType != null" >
        trade_type = #{tradeType,jdbcType=VARCHAR},
      </if>
      <if test="bankType != null" >
        bank_type = #{bankType,jdbcType=VARCHAR},
      </if>
      <if test="status != null" >
        status = #{status,jdbcType=BIT},
      </if>
      <if test="errCode != null" >
        err_code = #{errCode,jdbcType=VARCHAR},
      </if>
      <if test="errCodeDes != null" >
        err_code_des = #{errCodeDes,jdbcType=VARCHAR},
      </if>
      <if test="companyAccountId != null">
        company_account_id = #{companyAccountId},
      </if>
      <if test="bocPayType != null and bocPayType != ''">
        boc_pay_type = #{bocPayType},
      </if>
    </set>
    where order_no = #{orderNo}
    order by payment_id
    desc
  </update>

  <select id="selectPayTimeByOrderNo" resultType="java.time.LocalDateTime">
    select
      end_time
    from payment where
      order_no = #{orderNo} and status = 1 limit 1
    </select>

    <select id="selectPayTimeByOrderNoForceMaster" resultType="java.time.LocalDateTime">
    /*FORCE_MASTER*/
    select
      end_time
    from payment where
      order_no = #{orderNo} and status = 1 limit 1
    </select>

  <select id="selectByStatusAndTimeRange" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from payment
    where status = #{status}
    <if test="startTime != null">
      and create_time >= #{startTime}
    </if>
    <if test="endTime != null">
      and create_time &lt;= #{endTime}
    </if>
    order by payment_id
    <if test="limit != null">
      limit #{limit}
    </if>
  </select>

  <select id="selectByOrderNos" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from payment
    where order_no in
    <foreach collection="orderNos" item="orderNo" open="(" separator="," close=")">
      #{orderNo}
    </foreach>
    order by payment_id desc
  </select>
</mapper>