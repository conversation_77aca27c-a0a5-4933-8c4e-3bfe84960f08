<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.mall.mapper.MerchantStoreAccountAuthorizationRecordMapper">
  <resultMap id="BaseResultMap" type="net.summerfarm.mall.model.domain.merchant.MerchantStoreAccountAuthorizationRecord">
    <!--@mbg.generated-->
    <!--@Table merchant_store_account_authorization_record-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="m_id" jdbcType="BIGINT" property="mId" />
    <result column="account_id" jdbcType="BIGINT" property="accountId" />
    <result column="open_id" jdbcType="VARCHAR" property="openId" />
    <result column="mobile_phone" jdbcType="VARCHAR" property="mobilePhone" />
    <result column="store_name" jdbcType="VARCHAR" property="storeName" />
    <result column="store_type" jdbcType="VARCHAR" property="storeType" />
    <result column="sub_store_type" jdbcType="VARCHAR" property="subStoreType" />
    <result column="address_province" jdbcType="VARCHAR" property="addressProvince" />
    <result column="address_city" jdbcType="VARCHAR" property="addressCity" />
    <result column="address_region" jdbcType="VARCHAR" property="addressRegion" />
    <result column="address_street" jdbcType="VARCHAR" property="addressStreet" />
    <result column="registration_number" jdbcType="VARCHAR" property="registrationNumber" />
    <result column="company_name" jdbcType="VARCHAR" property="companyName" />
    <result column="corporation_name" jdbcType="VARCHAR" property="corporationName" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, m_id, account_id, open_id, mobile_phone, store_name, store_type, sub_store_type, 
    address_province, address_city, address_region, address_street, registration_number, 
    company_name, corporation_name, `status`, create_time, update_time
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from merchant_store_account_authorization_record
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    <!--@mbg.generated-->
    delete from merchant_store_account_authorization_record
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="net.summerfarm.mall.model.domain.merchant.MerchantStoreAccountAuthorizationRecord" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into merchant_store_account_authorization_record (m_id, account_id, open_id, 
      mobile_phone, store_name, store_type, 
      sub_store_type, address_province, address_city, 
      address_region, address_street, registration_number, 
      company_name, corporation_name, `status`)
    values (#{mId,jdbcType=BIGINT}, #{accountId,jdbcType=BIGINT}, #{openId,jdbcType=VARCHAR}, 
      #{mobilePhone,jdbcType=VARCHAR}, #{storeName,jdbcType=VARCHAR}, #{storeType,jdbcType=VARCHAR}, 
      #{subStoreType,jdbcType=VARCHAR}, #{addressProvince,jdbcType=VARCHAR}, #{addressCity,jdbcType=VARCHAR}, 
      #{addressRegion,jdbcType=VARCHAR}, #{addressStreet,jdbcType=VARCHAR}, #{registrationNumber,jdbcType=VARCHAR}, 
      #{companyName,jdbcType=VARCHAR}, #{corporationName,jdbcType=VARCHAR}, #{status,jdbcType=TINYINT})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="net.summerfarm.mall.model.domain.merchant.MerchantStoreAccountAuthorizationRecord" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into merchant_store_account_authorization_record
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="mId != null">
        m_id,
      </if>
      <if test="accountId != null">
        account_id,
      </if>
      <if test="openId != null">
        open_id,
      </if>
      <if test="mobilePhone != null">
        mobile_phone,
      </if>
      <if test="storeName != null">
        store_name,
      </if>
      <if test="storeType != null">
        store_type,
      </if>
      <if test="subStoreType != null">
        sub_store_type,
      </if>
      <if test="addressProvince != null">
        address_province,
      </if>
      <if test="addressCity != null">
        address_city,
      </if>
      <if test="addressRegion != null">
        address_region,
      </if>
      <if test="addressStreet != null">
        address_street,
      </if>
      <if test="registrationNumber != null">
        registration_number,
      </if>
      <if test="companyName != null">
        company_name,
      </if>
      <if test="corporationName != null">
        corporation_name,
      </if>
      <if test="status != null">
        `status`,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="mId != null">
        #{mId,jdbcType=BIGINT},
      </if>
      <if test="accountId != null">
        #{accountId,jdbcType=BIGINT},
      </if>
      <if test="openId != null">
        #{openId,jdbcType=VARCHAR},
      </if>
      <if test="mobilePhone != null">
        #{mobilePhone,jdbcType=VARCHAR},
      </if>
      <if test="storeName != null">
        #{storeName,jdbcType=VARCHAR},
      </if>
      <if test="storeType != null">
        #{storeType,jdbcType=VARCHAR},
      </if>
      <if test="subStoreType != null">
        #{subStoreType,jdbcType=VARCHAR},
      </if>
      <if test="addressProvince != null">
        #{addressProvince,jdbcType=VARCHAR},
      </if>
      <if test="addressCity != null">
        #{addressCity,jdbcType=VARCHAR},
      </if>
      <if test="addressRegion != null">
        #{addressRegion,jdbcType=VARCHAR},
      </if>
      <if test="addressStreet != null">
        #{addressStreet,jdbcType=VARCHAR},
      </if>
      <if test="registrationNumber != null">
        #{registrationNumber,jdbcType=VARCHAR},
      </if>
      <if test="companyName != null">
        #{companyName,jdbcType=VARCHAR},
      </if>
      <if test="corporationName != null">
        #{corporationName,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        #{status,jdbcType=TINYINT},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="net.summerfarm.mall.model.domain.merchant.MerchantStoreAccountAuthorizationRecord">
    <!--@mbg.generated-->
    update merchant_store_account_authorization_record
    <set>
      <if test="mId != null">
        m_id = #{mId,jdbcType=BIGINT},
      </if>
      <if test="accountId != null">
        account_id = #{accountId,jdbcType=BIGINT},
      </if>
      <if test="openId != null">
        open_id = #{openId,jdbcType=VARCHAR},
      </if>
      <if test="mobilePhone != null">
        mobile_phone = #{mobilePhone,jdbcType=VARCHAR},
      </if>
      <if test="storeName != null">
        store_name = #{storeName,jdbcType=VARCHAR},
      </if>
      <if test="storeType != null">
        store_type = #{storeType,jdbcType=VARCHAR},
      </if>
      <if test="subStoreType != null">
        sub_store_type = #{subStoreType,jdbcType=VARCHAR},
      </if>
      <if test="addressProvince != null">
        address_province = #{addressProvince,jdbcType=VARCHAR},
      </if>
      <if test="addressCity != null">
        address_city = #{addressCity,jdbcType=VARCHAR},
      </if>
      <if test="addressRegion != null">
        address_region = #{addressRegion,jdbcType=VARCHAR},
      </if>
      <if test="addressStreet != null">
        address_street = #{addressStreet,jdbcType=VARCHAR},
      </if>
      <if test="registrationNumber != null">
        registration_number = #{registrationNumber,jdbcType=VARCHAR},
      </if>
      <if test="companyName != null">
        company_name = #{companyName,jdbcType=VARCHAR},
      </if>
      <if test="corporationName != null">
        corporation_name = #{corporationName,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        `status` = #{status,jdbcType=TINYINT},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="net.summerfarm.mall.model.domain.merchant.MerchantStoreAccountAuthorizationRecord">
    <!--@mbg.generated-->
    update merchant_store_account_authorization_record
    set m_id = #{mId,jdbcType=BIGINT},
      account_id = #{accountId,jdbcType=BIGINT},
      open_id = #{openId,jdbcType=VARCHAR},
      mobile_phone = #{mobilePhone,jdbcType=VARCHAR},
      store_name = #{storeName,jdbcType=VARCHAR},
      store_type = #{storeType,jdbcType=VARCHAR},
      sub_store_type = #{subStoreType,jdbcType=VARCHAR},
      address_province = #{addressProvince,jdbcType=VARCHAR},
      address_city = #{addressCity,jdbcType=VARCHAR},
      address_region = #{addressRegion,jdbcType=VARCHAR},
      address_street = #{addressStreet,jdbcType=VARCHAR},
      registration_number = #{registrationNumber,jdbcType=VARCHAR},
      company_name = #{companyName,jdbcType=VARCHAR},
      corporation_name = #{corporationName,jdbcType=VARCHAR},
      `status` = #{status,jdbcType=TINYINT},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
<!--auto generated by MybatisCodeHelper on 2025-01-14-->
  <select id="selectAllByAccountId" resultMap="BaseResultMap">
    select <include refid="Base_Column_List"/>
    from merchant_store_account_authorization_record
    where account_id=#{accountId,jdbcType=BIGINT}
  </select>

  <select id="selectByOpenId" resultMap="BaseResultMap">
    select <include refid="Base_Column_List"/>
    from merchant_store_account_authorization_record
    where open_id=#{openId,jdbcType=VARCHAR}
    </select>
</mapper>